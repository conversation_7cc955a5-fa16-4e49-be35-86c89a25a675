using System;
using Microsoft.Playwright;
using SpecFlowProject.BusinessObjects;
using System.Globalization;
using System.Collections;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ClosedXML.Excel;
using NPOI.SS.UserModel;
using NPOI.HSSF.UserModel;
using SpecFlowProject.Utils;
using System.Text.RegularExpressions;
using Newtonsoft.Json.Linq;
using System.Linq.Expressions;
using static NPOI.HSSF.Util.HSSFColor;
using TechTalk.SpecFlow.Infrastructure;

namespace SpecFlowProject.Pom.Pages;


public class CheckbookPage: Base
{
    private ILocator columnDropDown => Page.FrameLocator("#v4Container").Locator(".columnEditorDropdown");

    private ILocator bdAmtCheckbox  => Page.FrameLocator("#v4Container").Locator("#initialBudget");

    private ILocator bdAmtCheckbox1  => Page.FrameLocator("#v4Container").Locator("#initialBudgetPeriod");
    private ILocator poACheckbox => Page.FrameLocator("#v4Container").Locator("#poApproval");
    private ILocator inACheckbox => Page.FrameLocator("#v4Container").Locator("#invoiceApproval");
    private ILocator SelectallCheckbox => Page.FrameLocator("#v4Container").Locator("div.k-list-header input");

    private ILocator bdRemainCheckbox  => Page.FrameLocator("#v4Container").Locator("#remainingInitialBudgetAmount");

    private ILocator bdPerceCheckbox  => Page.FrameLocator("#v4Container").Locator("#remainingInitialBudgetAmountPercentage");
    private ILocator fcAmtCheckbox  => Page.FrameLocator("#v4Container").Locator("#budgetAmount");
    private ILocator fcRemainCheckbox  => Page.FrameLocator("#v4Container").Locator("#remainingBudgetAmount");

    private ILocator fcPerceCheckbox  => Page.FrameLocator("#v4Container").Locator("#remainingBudgetAmountPercentage");

    private ILocator applyButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Apply')");

    private ILocator _departmentDropdown => Page.FrameLocator("#v4Container").Locator("main>div:nth-child(1) .k-dropdownlist");
    private ILocator _viewallLogsButton => Page.FrameLocator("#v4Container").Locator("button:has-text('View All Logs')");
    private ILocator _editButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Edit')");
    private ILocator _cancelButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Cancel')");
    private ILocator _clearFilters => Page.FrameLocator("#v4Container").Locator("button:has-text('Clear Filters')");
    private ILocator _saveButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Save')");
    private ILocator _periodViewButton => Page.FrameLocator("#v4Container").Locator("span:has-text('Period View')");
    private ILocator _yearViewButton => Page.FrameLocator("#v4Container").GetByText("Year View", new() { Exact = true }); 
    private ILocator _showActiveDropdown => Page.FrameLocator("#v4Container").Locator(".k-toolbar .k-dropdownlist").Nth(0);
    private ILocator _departmentDropdownClick => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(1)");
    private ILocator _departmentDropdownClickOrig => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(1)");
    private ILocator _activeDropdownLoopClick => Page.FrameLocator("#v4Container").Locator(".k-list ul li");
    private ILocator _activeDropdownDelete => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(5)");
    private ILocator _activeDropdownUL => Page.FrameLocator("#v4Container").Locator(".k-list ul");
    private ILocator _addNewGlCodeButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Add New GL Code')");
    private ILocator _deleteButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Delete')");
    private ILocator _unDeleteButton => Page.FrameLocator("#v4Container").Locator("button:has-text('UnDelete')");
    private ILocator _adjustByPercentButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Adjust Forecast By %')");
    private ILocator _adjustByPercentInput => Page.FrameLocator("#v4Container").Locator(".k-window-content input");
    private ILocator _caliculateButton => Page.FrameLocator("#v4Container").Locator(".k-widget button:has-text('Calculate')");
    private ILocator _clickFilter(int i) => Page.FrameLocator("#v4Container").Locator(".k-grid-header table thead tr:last-child th:nth-child("+i+") .k-grid-header-menu").Nth(0);
    private ILocator _filterDropdown => Page.FrameLocator("#v4Container").Locator(".k-filter-menu .k-dropdownlist");
    private ILocator _filterDropdownEquals => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(3)");
    private ILocator _filterDropdownoption(int i) => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child("+i+")");
    private ILocator _dropdownTreeCb => Page.FrameLocator("#v4Container").Locator("#period-dropdown"); 

    private ILocator _dropdownTreeCbExpand => Page.FrameLocator("#v4Container").Locator("#period-dropdown[aria-expanded='true']"); 
    private ILocator _dropdownTreeCbTree => Page.FrameLocator("#v4Container").Locator(".k-popup > .k-treeview > .k-treeview-lines > .k-treeview-item");
    private ILocator _amountText(int i) => Page.FrameLocator("#v4Container").Locator(".k-grid-table tbody tr td:nth-child("+i+") button");
    private ILocator _poTotals => Page.FrameLocator("#v4Container").Locator(".k-window-content table tbody tr td:nth-child(6)");
    private ILocator _textArea => Page.FrameLocator("#v4Container").Locator(".k-window-content .k-textarea textarea");
    private ILocator _clickPopupFilter => Page.FrameLocator("#v4Container").Locator(".k-dialog .k-grid-header table thead tr:last-child th:nth-child(4) .k-grid-header-menu");
    private ILocator _glCodeRowNoCopy(int i) => Page.FrameLocator("#v4Container").Locator(".view-year-checkbook table tbody tr:nth-child("+i+")");
    private ILocator _glCodeRow(int i) => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable table tbody tr:nth-child("+i+")");
    private ILocator _glCodeRowYear(int i) => Page.FrameLocator("#v4Container").Locator(".view-year-checkbook  table tbody tr:nth-child("+i+")");
    private ILocator _glCodeValYear(int i,int j) => _glCodeRowYear(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeDescYear(int i,int j) => _glCodeRowYear(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeVal(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeDesc(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeBudget(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeForecast(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeFixed(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") .k-switch");
    private ILocator _glCodeActiveYear(int i,int j) => _glCodeRowYear(i).Locator("td:nth-child("+j+") .k-switch");
    private ILocator _glCodeFixedYear(int i,int j) => _glCodeRowYear(i).Locator("td:nth-child("+j+") .k-switch");
    private ILocator _glCodeForcastAmt(int i,int j) => _glCodeRowYear(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeCopyAccross(int i,int j) => _glCodeRowYear(i).Locator("td:nth-child("+j+") button:has-text('Copy Across')");

    private ILocator _budgetForcast => Page.FrameLocator("#v4Container").Locator(".k-popup ul li:has-text('Budget and Forecast')");
    private ILocator _onlyBudget => Page.FrameLocator("#v4Container").Locator(".k-popup ul li:has-text('Only Budget')");
    private ILocator _onlyForcast => Page.FrameLocator("#v4Container").Locator(".k-popup ul li:has-text('Only Forecast')");
    private ILocator _glCodeActive(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") .k-switch");
    private ILocator _glCodeSelector(int i) => _glCodeRow(i).Locator("td:nth-child(1) input"); 
    private ILocator _glCodeJournelButton(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") button"); 
    private ILocator _glCodeViewButton(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") button"); 
    private ILocator _adjustBalanceGrid  => Page.FrameLocator("#v4Container").Locator("#adjustBalanceGrid table tbody tr:nth-child(1)");
    private ILocator _adjustBalanceGridInput => _adjustBalanceGrid.Locator("td:nth-child(12) input");
    private ILocator _adjustBalanceGridSelect => _adjustBalanceGrid.Locator("td:nth-child(11) .k-dropdownlist");
    private ILocator _allDepartments => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(1):has-text('All Departments')");
    private ILocator _FirstDepartments => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(2)");
    private ILocator _addClick => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(2)");
    private ILocator _updateButton => Page.FrameLocator("#v4Container").Locator(".k-dialog-buttongroup button:has-text('Update')");
    private ILocator _glCodeRows => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable table tbody tr");
    private ILocator _glCodeRowsNoCopyYear => Page.FrameLocator("#v4Container").Locator(".view-year-checkbook  table tbody tr");
    private ILocator _glCodeRowsYear => Page.FrameLocator("#v4Container").Locator(".view-year-checkbook table tbody tr");
    private ILocator _glTrans => Page.FrameLocator("#v4Container").Locator("#TransactionGrid table tbody tr");
    private ILocator _glTransFull => Page.FrameLocator("#v4Container").Locator("#TransactionGrid .k-grid-header table thead tr:lastchild th:nth-child(0) .k-grid-header-menu");
    private ILocator _approveBtnConfirm => _iframe.Locator(".k-dialog .k-dialog-buttongroup button:has-text('OK')");

    private ILocator hasUnsaved => _iframe.GetByRole(AriaRole.Button, new() { Name = "OK", Exact = true });
    private ILocator _okBtnConfirm => _iframe.Locator(".k-dialog .k-dialog-buttongroup button:has-text('Ok')");
    private ILocator _theadperiods => Page.FrameLocator("#v4Container").Locator("table thead tr th.budget-amt-header .k-column-title:has-text('Period')");
    private ILocator _plannedAmtLink(int i) => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable table tr:nth-child(1) td:nth-child("+i+") button");
    private ILocator _committedAmtLink(int i) => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable table tr:nth-child(1) td:nth-child("+i+") button");
    private ILocator _actualizedAmtLink(int i) => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable table tr:nth-child(1) td:nth-child("+i+") button");
    private ILocator _GLCodeFilter(int i) => Page.FrameLocator("#v4Container").Locator(".k-grid-header table thead tr:last-child th:nth-child("+i+") .k-grid-header-menu");
    private ILocator _ShowAll => _iframe.Locator(".k-window-content.k-dialog-content .k-tabstrip-items.k-reset li:nth-child(2)");
    private ILocator _plannedPOFilter => Page.FrameLocator("#v4Container").Locator("#holdAmountTransactionGrid .k-grid-header table thead tr:last-child th:nth-child(4) .k-grid-header-menu");
    private ILocator _committedPOFilter => Page.FrameLocator("#v4Container").Locator("#committedAmountTransactionGrid .k-grid-header table thead tr:last-child th:nth-child(4) .k-grid-header-menu");
    private ILocator _actualizedPOFilter => Page.FrameLocator("#v4Container").Locator("#actualizedTransactionGrid .k-grid-header table thead tr:last-child th:nth-child(4) .k-grid-header-menu");
    private ILocator _closeGrid => Page.FrameLocator("#v4Container").Locator(".k-dialog-buttongroup.k-actions.k-actions-stretched button");
    private ILocator _expandRow => Page.FrameLocator("#v4Container").Locator("#holdAmountTransactionGrid tbody tr:nth-child(1) td:nth-child(1) path");

    private ILocator budgetTxt(int i) => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable .k-grid-header thead tr:last-child th:nth-child("+i+")");

    private ILocator _selectAll => Page.FrameLocator("#v4Container").Locator(".k-grid-header table thead tr:first-child th:nth-child(1) input");
    private ILocator _bulkManage => Page.FrameLocator("#v4Container").Locator("button:has-text('Bulk Manage GL Code Attributes')");

    private ILocator labelLocator =>  Page.FrameLocator("#v4Container").Locator("label[aria-label='Always Exempt from Approval']");

    private ILocator assignAllUsers => Page.FrameLocator("#v4Container").Locator(".k-button-icon.k-icon.k-i-caret-double-alt-right");

    private ILocator sidePaneSave => Page.FrameLocator("#v4Container").Locator(".slide-pane button:has-text('Save')");

    private ILocator sidePaneCancel => Page.FrameLocator("#v4Container").Locator(".slide-pane button:has-text('Cancel')");
    private ILocator _okButton => Page.FrameLocator("#v4Container").Locator("button:has-text('OK')");
    private ILocator _InvoiceApprovalColumn => Page.FrameLocator("#v4Container").Locator(".k-grid-header table thead tr:last-child th:last-child");
    private ILocator _OffHyperlinkInvoice => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable tbody tr:first-child td:last-child button.text-link");

    private ILocator _OffHyperlinkPO => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable tbody tr:first-child td:nth-last-child(2) button.text-link");

    private ILocator sidePanelPOGrid => Page.FrameLocator("#v4Container").Locator(".slide-pane__content > div:last-child > div:first-child");

    private ILocator sidePanelInvoiceGrid => Page.FrameLocator("#v4Container").Locator(".slide-pane__content > div:last-child > div:last-child");

    private ILocator _AlwaysExemptPOfromApproval => sidePanelPOGrid.Locator("label[aria-label='Always Exempt from Approval']");
    private ILocator _AlwaysRequiredPOfromApproval => sidePanelPOGrid.Locator("label[aria-label='Always Require Approval']");
    private ILocator _AlwaysExemptInvoicefromApproval => sidePanelInvoiceGrid.Locator("label[aria-label='Always Exempt from Approval']");
    private ILocator _AlwaysRequiredInvoicefromApproval => sidePanelInvoiceGrid.Locator("label[aria-label='Always Require Approval']");
    private ILocator _selectallcheckboxofunassignedinpoapproval => sidePanelPOGrid.Locator("> div:last-child > div:last-child input").Nth(0);
    private ILocator _selectallcheckboxofunassignedininvoiceapproval => sidePanelInvoiceGrid.Locator("> div:last-child > div:last-child input").Nth(0);
    private ILocator _DoubleArrwbtninpoapproval => sidePanelPOGrid.Locator(".k-button-icon.k-icon.k-i-caret-double-alt-right");
    private ILocator _DoubleArrwbtnininvoiceapproval =>  sidePanelInvoiceGrid.Locator(".k-button-icon.k-icon.k-i-caret-double-alt-right");
    private ILocator _ImportExport => Page.FrameLocator("#v4Container").Locator("button:has-text('Import/Export')");
    private ILocator _FiscalYear => Page.FrameLocator("#v4Container").Locator("div.slide-pane__content>div:nth-child(3) div.mb-2:nth-child(5)");
    private ILocator _selectfiscalyear => Page.FrameLocator("#v4Container").Locator("div.k-list-content>ul li:nth-child(4)");
    private ILocator _Exportbtn => Page.FrameLocator("#v4Container").Locator(".flex.items-center.justify-end div button");
    private ILocator _propertydropdown => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(2) span span span");
    private ILocator _firstcompanydropdown => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(2)");
    private ILocator _singletabbtn => Page.FrameLocator("#v4Container").Locator("div.slide-pane__content>div:nth-child(3) div.mb-2:nth-child(4) ul>li:nth-child(2) input");
    private ILocator _txtbtn => Page.FrameLocator("#v4Container").Locator("div.slide-pane__content>div:nth-child(3) div.mb-2:nth-child(3) span:nth-child(2) input");
    private ILocator _allglcodes => Page.FrameLocator("#v4Container").Locator("div.slide-pane__content>div:nth-child(3) div.mb-2:nth-child(6) ul>li:nth-child(2) input");
    private ILocator _firstGLcode => Page.FrameLocator("#v4Container").Locator(".k-grid-container tr:first-child td:nth-child(2) span");
    private ILocator _includeforecastamt => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(7) div:nth-child(2) label:nth-child(2) span input");
    private ILocator _singlecomincludeforecastamt => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(2) label:nth-child(2) span input");
    private ILocator _singlecomallglcodes => Page.FrameLocator("#v4Container").Locator("div.slide-pane__content>div:nth-child(3) div.mb-2:nth-child(5) ul>li:nth-child(2) input");
    private ILocator _mgmtcompanydropdown => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(2) .k-dropdownlist button");
    private ILocator _innertextofmgmtcompany => Page.FrameLocator("#v4Container").Locator("div.k-list-content ul li:nth-child(12)");
    private ILocator _superadminpropertydropdown => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(3) span span span:nth-child(1)");
    private ILocator _superadmininnertextpropertydropdown => Page.FrameLocator("#v4Container").Locator("div.k-list-content ul li:nth-child(12)");
    private ILocator _fiscalyearofsuperadmin => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(6) span span span:nth-child(1)");
    private ILocator _selectfiscalyearofsuperadmin => Page.FrameLocator("#v4Container").Locator("div.k-list-content>ul li:nth-child(2)");
    private ILocator _superadmintxt => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) span:nth-child(2) input");
    private ILocator _superadminsingletab => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div :nth-child(5) li:nth-child(2) input");
    private ILocator _superadminallglcodes => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div :nth-child(5) li:nth-child(2) input");
    private ILocator _superadminsinglecomallglcodes => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div :nth-child(1) li:nth-child(2) input");
    private ILocator _superadminsinglecomfiscalyear => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(5) span span span:nth-child(1)");

    private ILocator _superadminincludeforecastamtbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(8) div:nth-child(2) label:nth-child(2) span input");
    private ILocator _superadminsinglecomincludeforecastamtbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(2) label:nth-child(2) span input");
    private ILocator _superadmintxtfiscalyear => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(5) span span span");
    private ILocator _superadmintxtinnerfiscalyear => Page.FrameLocator("#v4Container").Locator("div.k-list-content ul li:nth-child(3)");
    private ILocator _superadmintxtallglcodes => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div :nth-child(2) li:nth-child(2) input");
    private ILocator _superadmintxtforecastamtbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(2) label:nth-child(2) span input");
    private ILocator _Importsuperadminbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(2) div button:nth-child(2)");
    private ILocator _Importsuperadminfiscalyear => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(3) span span span");
    private ILocator _Importsuperadminselectfiscalyear => Page.FrameLocator("#v4Container").Locator("div.k-list-content ul li:nth-child(3)");
    private ILocator _Importbtn => Page.FrameLocator("#v4Container").Locator(".flex.items-center.justify-end div button");
    private ILocator _Proceedandimportbtn => Page.FrameLocator("#v4Container").Locator("button:has-text('Proceed and Import')");
    private ILocator _Draganddropbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(5)");
    private ILocator _Importbudgetamtbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div :nth-child(2) li:nth-child(1) input");
    private ILocator _ViewResultsbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(7) div p:nth-child(3)");
    private ILocator _Downloadbtn => Page.FrameLocator("#v4Container").Locator(".p-4.border-dashed.border.border-gray-300.flex.justify-between button");
    private ILocator _xiconinpopup => Page.FrameLocator("#v4Container").Locator(".k-window-actions.k-dialog-actions");
    private ILocator _txtbuttoninsuperadminforimport => Page.FrameLocator("#v4Container").Locator(".k-window-content.k-dialog-content div:nth-child(2) div ul li:nth-child(2) input");

    private ILocator sortedPO => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable table thead th:nth-child(12) .k-link");

    private ILocator _filterInput1 => _iframe.Locator(".k-filter-menu input");

    public CheckbookPage(IPage page) : base(page)
    {
        
    }
    
    private async Task SetGlCodes(CommonContext commonContext){
        string path = @".\\auth\\glcodes.txt"; 
        if (File.Exists(path) && commonContext.GLCodesData.Count == 0) 
        {
            string readText = File.ReadAllText(path); 
            Dictionary<string,GlStatus> totalData = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string,GlStatus>>(readText) ?? new Dictionary<string,GlStatus>();
            commonContext.GLCodesData  = totalData;
            await WaitForResponseAsync("api/GLCode/GLBudgetsByPeriod",commonContext);
        }else if(commonContext.GLCodesData.Count == 0){
            Newtonsoft.Json.Linq.JArray arr = await WaitForResponseAsync("api/GLCode/GLBudgetsByPeriod",commonContext);
            Dictionary<string,GlStatus> totalData = new Dictionary<string,GlStatus>();
            if(arr!=null){
                //Console.WriteLine("Inside");
                for (int i = 0; i < arr.Count; i++)
                {
                    #pragma warning disable CS8602 // Dereference of a possibly null reference.
                    var code = arr[i]["code"].ToString();
                    var invoiceApproval = GetType(arr[i]["invoiceGlApproval"]["status"].ToString());
                    var poApproval = GetType(arr[i]["poGlApproval"]["status"].ToString());
                    
                    #pragma warning disable CS8604 // Possible null reference argument.
                    var POC = arr[i]["poGlApproval"]["clientIds"].Values<long>().Count();
                    var IOC = arr[i]["invoiceGlApproval"]["clientIds"].Values<long>().Count();
                    var isActive = (bool)arr[i]["isActive"] && !(bool)arr[i]["isDeleted"];
                    #pragma warning restore CS8604 // Possible null reference argument.
                    #pragma warning restore CS8602 // Dereference of a possibly null reference.
                    if(poApproval == "Exempt"){
                        poApproval = POC>10?"Exempt":"Off";
                    }
                    if(invoiceApproval == "Exempt"){
                        invoiceApproval = IOC>10?"Exempt":"Off";
                    }
                    totalData.Add(code,new GlStatus(poApproval,invoiceApproval,isActive));
                }
                var newtotalData = totalData.OrderBy(x=>x.Value.POStatus).ThenBy(x=>x.Value.InvoiceStatus).ToDictionary(group => group.Key, group => group.Value);
                string content = Newtonsoft.Json.JsonConvert.SerializeObject(newtotalData); 
                File.WriteAllText(path, content);
                /*foreach(KeyValuePair<string, GlStatus> entry in newtotalData)
                {
                    Console.WriteLine(entry.Key);
                }*/
                commonContext.GLCodesData  = newtotalData;
            }else{
                Console.WriteLine("Call Failed");
                await _closeBtnX.ClickAsync();
                await sortedPO.ClickAsync();
                foreach(var elem in await _glCodeRows.AllAsync()){
                   var c = await elem.Locator("td:nth-child(3)").InnerTextAsync()??"";   
                   totalData.Add(c,new GlStatus("Off","Off",true));
                }
                string content = Newtonsoft.Json.JsonConvert.SerializeObject(totalData); 
                File.WriteAllText(path, content);
                commonContext.GLCodesData  = totalData;
            }
        }
        await Task.Run(()=>{});
    }

    public async Task ExpandAndOpenCheckBookReturn(CommonContext commonContext){
        string path = @".\\auth\\glcodes.txt"; 
        if(commonContext.GLCodesData.Count>0){
            return;
        }else if (File.Exists(path) && commonContext.GLCodesData.Count == 0) 
        {
            string readText = File.ReadAllText(path); 
            Dictionary<string,GlStatus> totalData = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string,GlStatus>>(readText) ?? new Dictionary<string,GlStatus>();
            commonContext.GLCodesData  = totalData;
        }else{
            await ExpandAndOpenCheckBook(commonContext);
        }
        await Task.Run(()=>{});
    }
    //Open checkbook
    public async Task OpenCheckbook(CommonContext commonContext){
        await _viewEditChecbook.ClickAsync();
        await SetGlCodes(commonContext);
        if(await IsVisibleAsync(_closeBtnX,1000,5)){
            await _closeBtnX.ClickAsync();
        }
        await HasDepartments(commonContext);
        var bdTxt = commonContext.HasDepartments?4:3;
        var txt = await budgetTxt(bdTxt).InnerTextAsync()??"";
        if(txt == "Budget"){
            commonContext.HasBudget  =true;
            await HideBudgetFields();
        }
    }
    //Open checkbook and filter by exempt GL codes
    public async Task OpenCheckbookandreturnExmptGLcodes(CommonContext commonContext ,bool cbOpened=false,string GLstatus="")
    {

        if (!cbOpened)
        {
            await _checkbookLink.ClickAsync();
        }
        await _viewEditChecbook.ClickAsync();
        await this.SelectCompany();
        await SetGlCodes(commonContext);
        if(await IsVisibleAsync(_closeBtnX,1000,5)){
            await _closeBtnX.ClickAsync();
        }

        await setupGLcodeStatusTo(GLstatus);        
    }


    public async Task setupGLcodeStatusTo(string status,bool first=true)
    {
        // Display PO and INA approval fields
        if (first)
        {
            await DisplayPoAndINAapprovalFields();
        }
        
        // Apply filter for status PO
        await SetFilter(3, status);

        // Apply filter for status Invoice
        await SetFilter(4, status);
    }

    private async Task SetFilter(int filterIndex, string status)
    {
        await this._clickFilter(filterIndex).WaitForAsync();
        await this._clickFilter(filterIndex).ClickAsync();
        await this._filterDropdown.WaitForAsync();
        await this._filterDropdown.ClickAsync();

        // Determine the option based on status
        int option = status switch
        {
            "Exempt" => 3,
            "Required" => 2,
            "OFF" => 1,
            _ => 0 // Default case, you might want to handle this scenario
        };

        if (option != 0)
        {
            await _filterDropdownoption(option).ClickAsync();
            await _filterBtn.ClickAsync();
        }
        else
        {
            Console.WriteLine("Invalid status provided.");
        }
    }

    public async Task SetupForecastAmountToLessThanZero()
    {
        await DisplayRemainingForcastAmtFields();
        await ApplyFilter(3, 5, "0");
        await ApplyFilter(4, 5, "0");
    }

    // Reusable filter setup method
    private async Task ApplyFilter(int filterIndex, int dropdownOptionIndex, string inputValue)
    {
        await this._clickFilter(filterIndex).WaitForAsync();
        await _clickFilter(filterIndex).ClickAsync();
        await _filterDropdown.WaitForAsync();
        await _filterDropdown.ClickAsync();
        await _filterDropdownoption(dropdownOptionIndex).ClickAsync();
        await _filterInput1.ClearAsync();
        await _filterInput1.FillAsync(inputValue);
        await _filterBtn.ClickAsync();
    }


    public async Task CaptureGLCode(CommonContext commonContext, ISpecFlowOutputHelper _specFlowOutputHelper)
    {
        if (!await IsVisibleAsync(_firstGLcode, 1000, 5))
        {
            throw new Exception("No GL Codes present for the selected approval state");
        }
    
        // Fetch the GL code
        commonContext.SpecificGL = await _firstGLcode.InnerTextAsync();
        _specFlowOutputHelper.WriteLine($"GLCode={commonContext.SpecificGL}");

        await Task.Run(()=>{});
    }




    // General helper method to handle checkbox clicks
    private async Task ApplyColumnFilter(IEnumerable<ILocator> checkboxes)
    {
        await columnDropDown.EvaluateAsync("node => node.click()");

        // Click each checkbox in the list
        foreach (var checkbox in checkboxes)
        {
            await checkbox.EvaluateAsync("node => node.click()");
        }

        await applyButton.ClickAsync();
    }

    private async Task HideBudgetFields()
    {
        // Pass the checkboxes specific to hiding budget fields
        await ApplyColumnFilter(new[] { bdAmtCheckbox, bdRemainCheckbox, bdPerceCheckbox });
    }

    private async Task DisplayPoAndINAapprovalFields()
    {
        // Pass the checkboxes specific to displaying PO and INA approval fields
        await ApplyColumnFilter(new[] { SelectallCheckbox, poACheckbox, inACheckbox });
    }

    private async Task DisplayRemainingForcastAmtFields()
    {
        // Pass the checkboxes specific to displaying remaining forecast fields
        await ApplyColumnFilter(new[] { fcAmtCheckbox, fcRemainCheckbox, fcPerceCheckbox });
    }


    public async Task ExpandAndOpenCheckBookWithGlMasks(CommonContext commonContext){
        await _checkbookLink.ClickAsync();
        JObject Jobject = await WaitUntilRequestCompleteResp("GetGlMasks",_viewGlMask,commonContext);
        if((bool?)Jobject["setGlMask"] == true){
            var glMasksToken = Jobject["glMasks"];
            if (glMasksToken != null)
            {
                string[] j = glMasksToken?.ToObject<string[]>() ?? Array.Empty<string>();
                List<string> filteredList = j.Where(x => x.Contains("*") || x.Contains("&") || x.Contains("#")).ToList();
                commonContext.GlMask = filteredList.First();
            }
        }
        await ExpandAndOpenCheckBook(commonContext,true);
    }

    private string GetType(string status){
        switch(status){
            case "1": return "Required";
            case "2": return "Exempt";
            default: return "Off";
        }
    }

    public async Task OpenCheckbookNoChecks(CommonContext commonContext){
        await _checkbookLink.ClickAsync();
        await _viewEditChecbook.ClickAsync();
        await WaitForResponseAsync("api/GLCode/GLBudgetsByPeriod",commonContext);
        await _closeBtnX.ClickAsync();
        await HasDepartments(commonContext);
        var bdTxt = commonContext.HasDepartments?4:3;
        var txt = await budgetTxt(bdTxt).InnerTextAsync()??"";
        if(txt == "Budget"){
            commonContext.HasBudget  =true;
            await HideBudgetFields();
        }
    }
    //Open Home and expand checkbook
    public async Task ExpandAndOpenCheckBook(CommonContext commonContext,bool cbOpened = false){
        if(!cbOpened){
            await _checkbookLink.ClickAsync();
        }
        await _viewEditChecbook.ClickAsync();
        await this.SelectCompany();
        await SetGlCodes(commonContext);
        if(await IsVisibleAsync(_closeBtnX,1000,5)){
            await _closeBtnX.ClickAsync();
        }
        await HasDepartments(commonContext);
        var bdTxt = commonContext.HasDepartments?4:3;
        var txt = await budgetTxt(bdTxt).InnerTextAsync()??"";
        if(txt == "Budget"){
            commonContext.HasBudget  =true;
            await HideBudgetFields();
        }
    }

    private async Task HasDepartments(CommonContext commonContext)
    {
        await _departmentDropdown.WaitForAsync();
        await _departmentDropdown.EvaluateAsync("node=>node.click()");
        await _activeDropdownLoopClick.First.WaitForAsync();
        var count = await _activeDropdownLoopClick.CountAsync();
        bool hasDepartments = count > 1 ?true:false;
        if(hasDepartments){
           commonContext.DepartmentName =  await _FirstDepartments.InnerTextAsync()??"";
        }else{
            var firstChild = await _activeDropdownLoopClick.First.InnerTextAsync()??"";
            if(!firstChild.Contains("All Departments")){
                commonContext.DepartmentName =  firstChild;
            }
        }
        await _departmentDropdown.EvaluateAsync("node=>node.click()");
        commonContext.HasDepartments=hasDepartments;
    }
    private async Task SelectPeriod(string period){
        await _dropdownTreeCb.EvaluateAsync("node=>node.click()");
        if(!await IsVisibleAsync(_dropdownTreeCbExpand,1000,5)){
            await _dropdownTreeCb.EvaluateAsync("node=>node.click()");
        }
        await _dropdownTreeCbTree.Locator(".k-treeview-leaf-text > span:has-text('"+period+"')").ClickAsync(); 
    }
    private async Task FilterGlCode(string glCode,int i){
        await _clickFilter(i).ClickAsync();
        await _filterDropdown.ClickAsync();
        await _filterDropdownEquals.ClickAsync();
        await _filterInput1.ClearAsync();
        await _filterInput1.FillAsync(glCode);
        await _filterBtn.ClickAsync();
    }

    private async Task FilterPoNumberPopup(string poNumber){
        await _ShowAll.ClickAsync();
        Task.Delay(2000).Wait();
        await _clickPopupFilter.ClickAsync();
        //Task.Delay(2000).Wait();
        //await _ShowAll.ClickAsync();
        Task.Delay(3000).Wait();
        await _filterInput1.ClearAsync();
        await _filterInput1.FillAsync(poNumber);
        await _filterBtn.ClickAsync();
    }

    private async Task VerifyTotal(float amt1){
        float totalCnt = 0;
        foreach(var amt in await _poTotals.AllAsync()){
            totalCnt+=GetAmount(await amt.TextContentAsync()??"0");
        }
        Assert.That(Math.Round(totalCnt, 2),Is.EqualTo(Math.Round(amt1, 2)));
        await _closeBtnX.ClickAsync();
    } 

    private async Task FilterPoNGL(int number,string poNumber,string glCode,int i){
        await FilterGlCode(glCode,i);
        await _amountText(number).ClickAsync();
        await FilterPoNumberPopup(poNumber);
    }
    
    //Verify Amounts on Checkbook grid
     public async Task VerifyPOWithAmounts(CommonContext commonContext, string amountType){
        int i = commonContext.HasDepartments?2:1;
        await Task.Run(()=>{});
        foreach(string ps in commonContext.Periods){
            var p = ps;
            if(p.IndexOf("(")!=-1){
                p = p.Split('(', ')')[1];
            }
            await SelectPeriod(p);
            Dictionary<string,Dictionary<string,ItemDetails>> poDict = GetDataAsDict(commonContext,true);
            Dictionary<string,Dictionary<string,ItemDetails>> invDict = GetDataAsDict(commonContext,false);
            if(amountType == "planned" || amountType == "committed"){
                if(poDict.ContainsKey(p)){
                    foreach(var t in poDict[p].Keys){
                        int number  = amountType == "planned"?commonContext.HasDepartments?6:5:commonContext.HasDepartments?7:6;
                        await FilterPoNGL(number,commonContext.PoNumber,t,i);
                        float amount = 0;
                        if(invDict.ContainsKey(p) && invDict[p].ContainsKey(t)){
                            amount-=invDict[p][t].getTotalAmount();
                        }
                        amount+=poDict[p][t].getTotalAmount();
                        await VerifyTotal(amount);
                    } 
                }
            }else{
                if(invDict.ContainsKey(p)){
                    foreach(var t in invDict[p].Keys){
                        int number  = commonContext.HasDepartments?8:7;
                        await FilterPoNGL(number,commonContext.PoNumber,t,i);
                        await VerifyTotal(invDict[p][t].getTotalAmount());
                    }
                }
            }
            
        }
    }

    //Verify Column Titles
     public async Task VerifyDataGrid(string column)
    {
        await this.VerifyTitle(column);
    }

    //Click Dept Dropdown
    public async Task ClickDepartments()
    {
        int totalGLCodes = await getTotalgridCount("GL");
        await this._departmentDropdown.WaitForAsync();
        await this._departmentDropdown.ClickAsync();
        await this.SelectDepartment(totalGLCodes);
    }

    private async Task SelectDepartment(int totalGLCodes)
    {
        await this._departmentDropdownClick.WaitForAsync();
        await this._departmentDropdownClick.ClickAsync();
        int count = await getTotalgridCount("GL");
        await this._departmentDropdown.ClickAsync();
        await this._departmentDropdownClickOrig.ClickAsync();
    }

    //Preset Filters
    public async Task SelectShowAllVerify()
    {
        await this._showActiveDropdown.ClickAsync();
        int dropdownCount = await _activeDropdownLoopClick.CountAsync();
        for(int i =1;i<=dropdownCount;i++){
            if(i==3){
                continue;   
            }
            await _activeDropdownUL.Locator("li:nth-child("+i+")").ClickAsync();
            int count = await getTotalgridCount("GL");
            //Assert.True(count>0);
            await _showActiveDropdown.ClickAsync();
        }
    }

    //Year View
    public async Task ClickYearView()
    {
        await _yearViewButton.ClickAsync();    
        await _closeBtnX.WaitForAsync();
        await _closeBtnX.ClickAsync();
    }
    
    public async Task VerifyCheckBook(CommonContext commonContext, bool hasReturn = false){
        await ExpandAndOpenCheckBook(commonContext);
        // Select the SelectAll checkbox in the column dropdown
        await columnDropDown.EvaluateAsync("node => node.click()");
        await SelectallCheckbox.EvaluateAsync("node => node.click()");
        await applyButton.ClickAsync();
        
        Console.WriteLine(hasReturn);
        if(hasReturn){
            await VerifyPOWithAmounts(commonContext, "planned");
            if (!string.IsNullOrEmpty(commonContext.PoUrl))
            {
                await Page.GotoAsync(commonContext.PoUrl,GetPgOptions());
            }
        }else{
            await VerifyPOWithAmounts(commonContext, "committed");
        }
    }
    //Adding GL Codes selecting diff periods
    public async Task AddGlCodesWithPeriod(CommonContext commonContext){
        string sMonth = DateTime.Now.ToString("MM");
        string sDay = DateTime.Now.ToString("dd");
        int d  = Int32.Parse(sDay);
        await columnDropDown.ClickAsync();
        var hasBudget = await IsVisibleAsync(bdAmtCheckbox1,1000,5);
        await applyButton.ClickAsync();
        int per = 1;
        //int totalPeriods = await _theadperiods.CountAsync();
        foreach(var row in await _theadperiods.AllAsync()){
            var period = await row.InnerTextAsync();
            period = period.Split('(', ')')[1];
            var periods = period.Split(" - ");
            var yearMonth = periods[0].Split("/");
            var yearMonthEnd = periods[1].Split("/");
            if((yearMonth[0]==sMonth || yearMonthEnd[0] == sMonth) && Int32.Parse(yearMonthEnd[1])>=d){
                break;
            }else{
                per++;
            }
        }
        string baseGl = RandomGenerator.GenerateGlCode(commonContext.GlMask??"####-##",commonContext.GLCodesData);
        int totalGLCodes = await getTotalgridCount("GL");
        Console.WriteLine("per"+ per+""+hasBudget);
        if(hasBudget){
            per = ((per-1) *2)+1;
        };
        foreach(GlCodes glCodes in commonContext.GLCodesList){
            await _addNewGlCodeButton.ClickAsync();
            string finalVal = baseGl;
            glCodes.GLCode = finalVal;
            glCodes.GLCodeDescription = baseGl;
            await _glCodeValYear(1,commonContext.HasDepartments?4:3).FillAsync(finalVal);
            await _glCodeDescYear(1,commonContext.HasDepartments?5:4).FillAsync(baseGl);
            if(glCodes.FixedCode == "no"){
                await _glCodeFixedYear(1,commonContext.HasDepartments?6:5).ClickAsync();
            }
            if(glCodes.ActiveCode == "no"){
                await _glCodeActiveYear(1,commonContext.HasDepartments?7:6).ClickAsync();
            }
            int k = commonContext.HasDepartments?8+per:7+per;
            if(commonContext.HasBudget){
                await _glCodeForcastAmt(1,k).FillAsync(glCodes.BudgetAmount??"0");
                k=k+1;
            }
            await _glCodeForcastAmt(1,k).FillAsync(glCodes.ForcastAmount??"0");
            await _glCodeCopyAccross(1,commonContext.HasDepartments?8:7).ClickAsync();
            if(commonContext.HasBudget){
                await _budgetForcast.ClickAsync();
                await _glCodeCopyAccross(1,commonContext.HasDepartments?8:7).ClickAsync();
                await _onlyBudget.ClickAsync();
                await _glCodeCopyAccross(1,commonContext.HasDepartments?8:7).ClickAsync();
                await _onlyForcast.ClickAsync();
            }
        }
        
        await _saveButton.ClickAsync();
        if(await IsVisibleAsync(hasUnsaved,1000,5)){
            await hasUnsaved.ClickAsync();
        }
        if(await IsVisibleAsync(_okBtnConfirm,1000,5)){
            await _okBtnConfirm.ClickAsync();
        }  
        if(await IsVisibleAsync(_okBtnConfirm,1000,5)){
            await _okBtnConfirm.ClickAsync();
        }   
        if(commonContext.HasDepartments && !string.IsNullOrEmpty(commonContext.DepartmentName)){
            await AddGLCodesToDepartment(commonContext.DepartmentName);
        }                                       
        await _yearViewButton.ClickAsync();
        await _closeBtnX.WaitForAsync();
        await _closeBtnX.ClickAsync();
        
        int final = await getTotalgridCount("GL");
        if(commonContext.HasDepartments && final>totalGLCodes){
            commonContext.GLAdded = true;
            await _glCodeRowsNoCopyYear.First.WaitForAsync();
            await FilterAndClick(baseGl, true, commonContext.HasDepartments);
            await _glCodeRowsNoCopyYear.First.WaitForAsync();
            foreach(var elem in await _glCodeRowsNoCopyYear.AllAsync()){
                await Assertions.Expect(elem.Locator("td:nth-child(3)")).ToContainTextAsync(baseGl);   
                await Assertions.Expect(elem.Locator("td:nth-child(4)")).ToContainTextAsync(baseGl);  
            }
        }
    }

    //Add New GL Code
    public async Task AddNewGlCodes(CommonContext commonContext){
        string baseGl = RandomGenerator.GenerateGlCode(commonContext.GlMask??"####-##",commonContext.GLCodesData);
        int totalGLCodes = await getTotalgridCount("GL");
        foreach(GlCodes glCodes in commonContext.GLCodesList){
            await _addNewGlCodeButton.ClickAsync();
            string finalVal = baseGl;
            glCodes.GLCode = finalVal;
            glCodes.GLCodeDescription = baseGl;
            commonContext.SpecificGL = finalVal;
            await _glCodeVal(1,commonContext.HasDepartments?4:3).FillAsync(finalVal);
            await _glCodeDesc(1,commonContext.HasDepartments?5:4).FillAsync(baseGl);
            commonContext.ExemptGL=finalVal;
            await _glCodeForecast(1,commonContext.HasDepartments?6:5).FillAsync(glCodes.ForcastAmount ?? "1");
            if(glCodes.FixedCode == "no"){
                await _glCodeFixed(1,commonContext.HasDepartments?14:13).ClickAsync();
            }
            if(glCodes.ActiveCode == "no"){
                await _glCodeActive(1,commonContext.HasDepartments?15:14).ClickAsync();
            }
        }
        await _saveButton.ClickAsync();
        if(await IsVisibleAsync(hasUnsaved,1000,5)){
            await hasUnsaved.ClickAsync();
        }  
        if(await IsVisibleAsync(_okBtnConfirm,1000,5)){
            await _okBtnConfirm.ClickAsync();
        }
        if(await IsVisibleAsync(_okBtnConfirm,1000,5)){
            await _okBtnConfirm.ClickAsync();
        }
        if(commonContext.HasDepartments && !string.IsNullOrEmpty(commonContext.DepartmentName)){
            await AddGLCodesToDepartment(commonContext.DepartmentName);
        }  
        Task.Delay(10000).Wait();
        int final = await getTotalgridCount("GL");
        if(commonContext.HasDepartments && final>totalGLCodes){
           commonContext.GLAdded = true;
            await _glCodeRows.First.WaitForAsync();
            await FilterAndClick(baseGl, true,commonContext.HasDepartments);
            await _glCodeRows.First.WaitForAsync();
            foreach(var elem in await _glCodeRows.AllAsync()){
                await Assertions.Expect(elem.Locator("td:nth-child(3)")).ToContainTextAsync(baseGl);   
                await Assertions.Expect(elem.Locator("td:nth-child(4)")).ToContainTextAsync(baseGl); 
                // await Assertions.Expect(elem.Locator("td:nth-child(5)")).ToContainTextAsync("$20.00");  
                // await Assertions.Expect(elem.Locator("td:nth-child(6)")).ToContainTextAsync("1");
            } 
        }
    }

    private async Task FilterAndClick(string  value, bool isEqual, bool _hasDepartments){
        await _clickFilter(_hasDepartments?2:1).ClickAsync();
        if(isEqual){
            await _filterDropdown.ClickAsync();
            await _filterDropdownEquals.ClickAsync();
        }
        await _filterInput1.ClearAsync();
        await _filterInput1.FillAsync(value);
        await _filterBtn.ClickAsync();
    }

    //Adjust Forecast Amounts
    public async Task AdjustForecastAmts(CommonContext commonContext){
        #pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
        GlCodes element = commonContext.GLCodesList.Find(e => e.FixedCode == "no");

        #pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.
        if (element!=null && commonContext.HasDepartments && commonContext.GLAdded){
            await FilterAndClick(element.GLCode??"0", true, commonContext.HasDepartments);
            await _glCodeSelector(1).EvaluateAsync("node=>node.click()");
            await _editButton.ClickAsync(); 
            await _adjustByPercentButton.ClickAsync();   
            await _adjustByPercentInput.FillAsync("200");
            await _caliculateButton.ClickAsync();
            await _saveButton.ClickAsync();
            await FilterAndClick(element.GLCode??"0", true,commonContext.HasDepartments);
            await _glCodeSelector(1).EvaluateAsync("node=>node.click()");
            await _glCodeRows.First.WaitForAsync();
            foreach(var elem in await _glCodeRows.AllAsync()){
                if(commonContext.HasDepartments){
                    await Assertions.Expect(elem.Locator("td:nth-child(5)")).ToContainTextAsync("2.00"); 
                }else{
                    await Assertions.Expect(elem.Locator("td:nth-child(4)")).ToContainTextAsync("2.00"); 
                }    
            }
        }

    }

    private async Task AdjustJournel(GlCodes element, bool isAdd, bool _hasDepartments){
        await _glCodeJournelButton(1,_hasDepartments?15:11).ClickAsync(); 
        await _adjustBalanceGridInput.FillAsync("1");
        if(isAdd){
            await _adjustBalanceGridSelect.ClickAsync();
            await _addClick.ClickAsync();
        }
        await _textArea.FillAsync("Avendra");
        await _updateButton.ClickAsync();
    }

    //Add New Journal Entry
    public async Task AddJournalEntry(CommonContext commonContext){
#pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
        GlCodes element = commonContext.GLCodesList.Find(e => e.FixedCode == "yes");
#pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.
        if (element!=null  && commonContext.HasDepartments && commonContext.GLAdded){
            await FilterAndClick(element.GLCode??"0", true, commonContext.HasDepartments);
            await _glCodeSelector(1).EvaluateAsync("node=>node.click()");
            await _editButton.ClickAsync();     
            await AdjustJournel(element,false,commonContext.HasDepartments);
            await AdjustJournel(element,true,commonContext.HasDepartments);
            foreach(var elem in await _glCodeRows.AllAsync()){
                if(commonContext.HasDepartments){
                    await Assertions.Expect(elem.Locator("td:nth-child(9)")).ToContainTextAsync("0.00"); 
                }else{
                    await Assertions.Expect(elem.Locator("td:nth-child(8)")).ToContainTextAsync("0.00"); 
                }  
            }
            await _glCodeViewButton(1,commonContext.HasDepartments?16:12).ClickAsync();
            await _glTrans.First.WaitForAsync();
            await Assertions.Expect(_glTrans).ToHaveCountAsync(2);   
            await _closeBtnX.ClickAsync();
            await _viewallLogsButton.ClickAsync();
            await _glTransFull.ClickAsync();
            await _filterInput1.ClearAsync();
            await _filterInput1.FillAsync(element.GLCode??"0");
            await _filterBtn.ClickAsync();
            await _glTrans.First.WaitForAsync();
            await Assertions.Expect(_glTrans).ToHaveCountAsync(2);  
            await _closeBtnX.ClickAsync();
            await _cancelButton.ClickAsync();  
        }
        await Task.Run(()=>{});
    }

    //Edit Gl codes
    public async Task EditGlCodes(CommonContext commonContext, bool isYearView)
    {
        #pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
        GlCodes element = commonContext.GLCodesList.Find(e => e.FixedCode == "yes");
        #pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.
        if (element!=null  && commonContext.HasDepartments && commonContext.GLAdded)
        {
            await _editButton.ClickAsync(); 
            if(!isYearView)
            {
                await _glCodeDesc(1,commonContext.HasDepartments?5:4).FillAsync("sss");
                await _glCodeForecast(1,commonContext.HasDepartments?6:5).FillAsync("1");
            }
            else
            {

                await _glCodeDescYear(1,commonContext.HasDepartments?5:4).FillAsync("sss");
            }
            await _saveButton.ClickAsync();
        }
        await Task.Run(()=>{});
    }

    //Delete GL Codes
    public async Task DeleteGLCodes(CommonContext commonContext, bool isYearView)
    {
        if(commonContext.HasDepartments && commonContext.GLAdded)
        {
            await _clearFilters.ClickAsync();
            #pragma warning disable CS8604 // Possible null reference argument.
            await FilterAndClick(commonContext.GLCodesList[0].GLCodeDescription, true, commonContext.HasDepartments);
            #pragma warning restore CS8604 // Possible null reference argument.
            var rowView = isYearView?_glCodeRowsNoCopyYear:_glCodeRows;
            await rowView.First.WaitForAsync();
            foreach(var elem in await rowView.AllAsync())
            {
                await elem.Locator("td:nth-child(1) input").EvaluateAsync("node=>node.click()");
            }
            await _deleteButton.ClickAsync();
            await _approveBtnConfirm.ClickAsync();
        }
        await Task.Run(()=>{});
       
    }

    //UNDelete Gl Codes
    public async Task UnDeleteGLCodes(CommonContext commonContext, bool isYearView)
    {
        if(commonContext.HasDepartments && commonContext.GLAdded)
        {
            await _showActiveDropdown.ClickAsync();
            await _activeDropdownDelete.ClickAsync();
            #pragma warning disable CS8604 // Possible null reference argument.
            await FilterAndClick(commonContext.GLCodesList[0].GLCodeDescription, true,commonContext.HasDepartments);
            #pragma warning restore CS8604 // Possible null reference argument.
            var rowView = isYearView?_glCodeRowsNoCopyYear:_glCodeRows;
            await rowView.First.WaitForAsync();
            foreach(var elem in await rowView.AllAsync())
            {
                await elem.Locator("td:nth-child(1) input").EvaluateAsync("node=>node.click()");
            }
            await _unDeleteButton.ClickAsync();
            await _approveBtnConfirm.ClickAsync();
        }
        await Task.Run(()=>{});
    }

    public async Task VerifyPlannedAmt(CommonContext commonContext)
    {
        var ple = commonContext.HasDepartments?2:1;
        await _GLCodeFilter(ple).ClickAsync();
        await _filterInput1.FillAsync(commonContext.GlCodes[0]);
        if(await _filterBtn.IsEnabledAsync()){
            await _filterBtn.ClickAsync();
            Task.Delay(2000).Wait();
            var pl = commonContext.HasDepartments?6:5;
            await _plannedAmtLink(pl).ClickAsync();
            Task.Delay(2000).Wait();
            await _ShowAll.ClickAsync();
            await _plannedPOFilter.ClickAsync();
            await _filterInput1.FillAsync(commonContext.PoNumber);
            await _filterBtn.ClickAsync();
            Task.Delay(2000).Wait();
            ILocator frameElementHandle = Page.Locator("#v4Container");
            await frameElementHandle.WaitForAsync();
            var rows = frameElementHandle.Locator("#holdAmountTransactionGrid tbody tr");
            string[] expectedStatuses = { "PO Accepted", "PO Submitted", "PO Declined", "PO Submitted", "PO Recalled", "PO Submitted" };
            for (int i = 0; i < Math.Min(await rows.CountAsync(), expectedStatuses.Length); i++)
            {
                var statusElement = rows.Nth(i+1).Locator("td:nth-child(5) span");
                var statusText = statusElement != null ? await statusElement.TextContentAsync() : "Status not found";
                //var statusMessage = statusText.Trim().Equals(expectedStatuses[i], StringComparison.OrdinalIgnoreCase) ? 
                                   // $"Row {i + 1} status is correct." : 
                                   // $"Row {i + 1} status is incorrect. Expected: {expectedStatuses[i]}, Found: {statusText}";
                
                var amountElement = rows.Nth(i+1).Locator("td:nth-child(6) span");
                var amountText = amountElement != null ? await amountElement.TextContentAsync() : "Amount not found";
                var cleanedAmountText = Regex.Replace(amountText?.Trim() ?? string.Empty, @"[^\d.-]", "");
                if (decimal.TryParse(cleanedAmountText, NumberStyles.Any, CultureInfo.InvariantCulture, out var amount))
                {
                    if (commonContext.PeriodsItemPrices.Count >= 2)
                    {
                        bool isNegativeStatus = statusText != null && (statusText.Trim().Equals("PO Accepted", StringComparison.OrdinalIgnoreCase) ||
                                        statusText.Trim().Equals("PO Declined", StringComparison.OrdinalIgnoreCase) ||
                                        statusText.Trim().Equals("PO Recalled", StringComparison.OrdinalIgnoreCase));

                        decimal expectedAmount = isNegativeStatus ? 
                                    -Math.Abs(Convert.ToDecimal(commonContext.PeriodsItemPrices[0])) : 
                                    Math.Abs(Convert.ToDecimal(commonContext.PeriodsItemPrices[0]));
                        /*var amountMessage = amount == expectedAmount ? 
                                        $"Row {i + 1} amount is correct." : 
                                        $"Row {i + 1} amount is incorrect. Expected: {expectedAmount}, Found: {amount}";*/
                    
                    }
                    else
                    {
                        Console.WriteLine("Error: 'commonContext.PeriodsItemPrices' does not contain enough elements.");
                    }
                }
                else
                {
                    Console.WriteLine($"Row {i + 1} amount is invalid. Found: {amountText}");
                }
            }
            await _closeGrid.ClickAsync();
        }
        await Task.Run(()=>{});
    }

    public async Task VerifyCommittedAmt(CommonContext commonContext)
    {
        var pl = commonContext.HasDepartments?7:6;
        await _committedAmtLink(pl).ClickAsync();
        Task.Delay(2000).Wait();
        await _ShowAll.ClickAsync();
        await _committedPOFilter.ClickAsync();
        await _filterInput1.FillAsync(commonContext.PoNumber);
        if(await _filterBtn.IsEnabledAsync()){
            await _filterBtn.ClickAsync();
            Task.Delay(2000).Wait();
            ILocator frameElementHandle = Page.Locator("#v4Container");
            await frameElementHandle.WaitForAsync();
            var rows = frameElementHandle.Locator("#committedAmountTransactionGrid tbody tr");
            string[] expectedStatuses = { "PO Closed", "Credit Memo Completed", "Invoice Completed", "PO Accepted" };

            for (int i = 0; i < Math.Min(await rows.CountAsync(), expectedStatuses.Length); i++)
            {
                var statusElement = rows.Nth(i+1).Locator("td:nth-child(5) span");
                var statusText = statusElement != null ? await statusElement.TextContentAsync() : "Status not found";
                if (statusText != null && (statusText.Contains("Invoice Completed") || statusText.Contains("Credit Memo Completed")))
                {
                    statusText = Regex.Replace(statusText, @"\d", "").Trim();
                }
                /*var statusMessage = statusText.Trim().Equals(expectedStatuses[i], StringComparison.OrdinalIgnoreCase) ? 
                                    $"Row {i + 1} status is correct." : 
                                    $"Row {i + 1} status is incorrect. Expected: {expectedStatuses[i]}, Found: {statusText}";*/
                
            }
            await _closeGrid.ClickAsync();
        }
        await Task.Run(()=>{});
    }

    public async Task VerifyActualizedAmt(CommonContext commonContext)
    {
        var pl = commonContext.HasDepartments?8:7;
        await _actualizedAmtLink(pl).ClickAsync();
        Task.Delay(2000).Wait();
        await _actualizedPOFilter.ClickAsync();
        await _filterInput1.FillAsync(commonContext.PoNumber);
        if(await _filterBtn.IsEnabledAsync()){
            await _filterBtn.ClickAsync();
            Task.Delay(2000).Wait();
            ILocator frameElementHandle = Page.Locator("#v4Container");
            await frameElementHandle.WaitForAsync();
            var rows = frameElementHandle.Locator("#actualizedTransactionGrid tbody tr");
            string[] expectedStatuses = { "Credit Memo Completed", "Invoice Completed" };

            for (int i = 0; i < Math.Min(await rows.CountAsync(), expectedStatuses.Length); i++)
            {
                var statusElement = rows.Nth(i+1).Locator("td:nth-child(5) span");
                var statusText = statusElement != null ? await statusElement.TextContentAsync() : "Status not found";
                if (statusText != null && (statusText.Contains("Invoice Completed") || statusText.Contains("Credit Memo Completed")))
                {
                    statusText = Regex.Replace(statusText, @"\d", "").Trim();
                }
                /*var statusMessage = statusText.Trim().Equals(expectedStatuses[i], StringComparison.OrdinalIgnoreCase) ? 
                                    $"Row {i + 1} status is correct." : 
                                    $"Row {i + 1} status is incorrect. Expected: {expectedStatuses[i]}, Found: {statusText}";*/
                
            }
            await _closeGrid.ClickAsync();
        }
    }

    public async Task ExemptAllGLCodes(){
        await _selectAll.ClickAsync();
        await _bulkManage.ClickAsync();
        foreach(var lab in await labelLocator.AllAsync()){
            await lab.ClickAsync();
        }
        foreach(var lab in await assignAllUsers.AllAsync()){
            await lab.ClickAsync();
        }
        await sidePaneSave.ClickAsync();
        await _okButton.ClickAsync();
        await sidePaneCancel.ClickAsync();
    }

    private async Task CommonSelectPO(bool isRequired){
        if(isRequired)
            await _AlwaysExemptPOfromApproval.ClickAsync();
        else    
            await _AlwaysRequiredPOfromApproval.ClickAsync();
        await _selectallcheckboxofunassignedinpoapproval.ClickAsync();
        await _DoubleArrwbtninpoapproval.ClickAsync();
        await sidePaneSave.ClickAsync();
        await sidePaneCancel.ClickAsync();
    }
    public async Task ExemptGLCodeinPOWorkflow()
    {
        await _editButton.ClickAsync();
        await _InvoiceApprovalColumn.ScrollIntoViewIfNeededAsync();
        bool isRequired = await _OffHyperlinkPO.InnerTextAsync()=="Required"?true:false;
        await _OffHyperlinkPO.ClickAsync();
        await CommonSelectPO(isRequired);
        var text = await _OffHyperlinkPO.InnerTextAsync();
        Assert.That(text,Is.EqualTo(isRequired?"Exempt":"Required"));
        await _OffHyperlinkPO.ClickAsync();
        await CommonSelectPO(!isRequired);
        text = await _OffHyperlinkPO.InnerTextAsync();
        Assert.That(text,Is.EqualTo(isRequired?"Required":"Exempt"));
    }

    private async Task CommonSelectInvoice(bool isRequired){
        if(isRequired)
            await _AlwaysExemptInvoicefromApproval.ClickAsync();
        else    
            await _AlwaysRequiredInvoicefromApproval.ClickAsync();
        await _selectallcheckboxofunassignedininvoiceapproval.ClickAsync();
        await _DoubleArrwbtnininvoiceapproval.ClickAsync();
        await sidePaneSave.ClickAsync();
        await sidePaneCancel.ClickAsync();
    }
    public async Task ExemptGLCodeinInvoiceWorkflow()
    {
        await _editButton.ClickAsync();
        await _InvoiceApprovalColumn.ScrollIntoViewIfNeededAsync();
        bool isRequired = await _OffHyperlinkInvoice.InnerTextAsync()=="Required"?true:false;
        await _OffHyperlinkInvoice.ClickAsync();
        await CommonSelectInvoice(isRequired);
        var text = await _OffHyperlinkInvoice.InnerTextAsync();
        Assert.That(text,Is.EqualTo(isRequired?"Exempt":"Required"));
        await _OffHyperlinkInvoice.ClickAsync();
        await CommonSelectInvoice(!isRequired);
        text = await _OffHyperlinkInvoice.InnerTextAsync();
        Assert.That(text,Is.EqualTo(isRequired?"Required":"Exempt"));
    }    
}
