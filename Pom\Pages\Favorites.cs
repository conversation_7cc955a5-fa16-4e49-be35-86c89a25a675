using Microsoft.Playwright;
using Newtonsoft.Json.Linq;
using SpecFlowProject.Utils;
using SpecFlowProject.BusinessObjects;
using System;
using System.Threading.Tasks;

namespace SpecFlowProject.Pom.Pages;
public class Favorites: Base
{
    private ILocator _favoritesProductsLink => Page.Locator("h2:has-text('Favorite Products')");
    private ILocator _ManageFolders => Page.Locator("a:has-text('Manage Folders')");
    private ILocator _NewFolder => Page.Locator("#newButton");
    private ILocator _FolderName => Page.Locator("#favMaintName");
    private ILocator _SelectGLCode => Page.Locator("#favoriteFolderSelectGLCode li:nth-child(1)");
    private ILocator _SaveFolder => Page.Locator("#saveButton");
    private ILocator _shareFolder => Page.Locator("#shareButton");
    private ILocator _ApplyButton => Page.Locator("button:has-text('Apply')");
    private ILocator _closeButton => Page.Locator("//html//body//div[24]//div[11]//div//button[2]");
    private ILocator _SharedFolderName => Page.Locator("#favoriteFolderSelectCopy").GetByText("Shared Folder", new() { Exact = true });
    private ILocator _OwnedFolderName => Page.Locator("#favoriteFolderSelectCopy li:has-text('Owned Folder')");
    private ILocator _companiesList => Page.Locator("#dialog-operation-copyshare #treegridCompanies tbody tr");
    private ILocator _CompaniesUser => Page.Locator("#dialog-operation-copyshare #gview_treegridUsers tbody tr");
    private ILocator _successToaster => Page.Locator(".toast.toast-success");
    private ILocator _selectLineItem => Page.Locator(".checkboxContainer input");
    private ILocator _addProducts => Page.Locator("#btnFavoriteFolderAdd");
    private ILocator _FolderSelect => Page.Locator("#favoriteFoldersListBox");
    private ILocator _AddFavoritesBtn => Page.Locator("button:has-text('Add to Favorites')");
    private ILocator _OpenOwnedFolder => Page.Locator("a:has-text('Owned Folder')");
    private ILocator _OpenSharedFolder => Page.GetByRole(AriaRole.Link, new() { Name = "Shared Folder", Exact = true });
    private ILocator _filterItem => Page.Locator("#filterTextbox");
    private ILocator _supplierName => Page.Locator("#favoriteSummaryGrid tbody tr:nth-child(2) td:nth-child(7)");
    private ILocator _summaryViewButton => Page.Locator("#summaryButton");

    // Locators for the new line item fields
    private ILocator _currentDocQtyField => _iframe.Locator(".k-grid-content tr:last-child td:nth-child(6) input");
    private ILocator _itemNumberField => _iframe.Locator(".k-grid-content tr:last-child td:nth-child(8) input");
    private ILocator _descriptionField => _iframe.Locator(".k-grid-content tr:last-child td:nth-child(9) input");
    private ILocator _glCodeDropdown => _iframe.Locator(".k-grid-content tr:last-child td:nth-child(10) span.k-dropdownlist span span");
    private ILocator _glCodeList => _iframe.Locator(".k-list ul li:nth-child(1)");
    private ILocator _uomField => _iframe.Locator(".k-grid-content tr:last-child td:nth-child(11) input");
    private ILocator _priceField => _iframe.Locator(".k-grid-content tr:last-child td:nth-child(12) input");
    private ILocator Supplier =>_iframe.Locator(".slide-pane__content .k-dropdownlist");
    private ILocator CreateButton => _iframe.Locator(".flex.mt-2 div:nth-child(1) button");
    private ILocator _dropdownListItem => Page.FrameLocator("#v4Container").Locator("ul[role='listbox']");
    private ILocator _confirmCreate => _iframe.GetByRole(AriaRole.Button, new() { Name = "Confirm", Exact = true });
    private ILocator _invPopupBtn => _iframe.Locator(".k-window-actions.k-dialog-actions button");
    private ILocator _ownGLCode => Page.Locator("#favoriteFolderSelectGLCode li");
    private ILocator _currentDocQty => _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child(1) td:nth-child(6) input");
    private ILocator _changePObtn => _iframe.Locator("button:has-text('Change PO Assignment')");
    private ILocator _searchPO => _iframe.Locator(".k-window-content div:nth-child(2) div div input").Nth(0);
    private ILocator _nextBtn => _iframe.Locator(".k-window-content div nav button");
    private ILocator _applyToThisPO => _iframe.Locator(".k-window-content div nav button").Nth(0);

    public Favorites(IPage page) : base(page)
    {
    }

    public async Task OpenFavorites(CommonContext commonContext)
    {
        await _favoritesProductsLink.ClickAsync();
    }

    public async Task AddNewOwnedFolder(CommonContext commonContext)
    {

        await _ManageFolders.ClickAsync();
        bool isFolderPresent = await _OwnedFolderName.IsVisibleAsync();
        if (isFolderPresent)
        {
            await _OwnedFolderName.ClickAsync();
            commonContext.OwnedGLCode = await _ownGLCode.TextContentAsync();
            commonContext.OwnedGLCode= ExtractGLCode(commonContext.OwnedGLCode);
            commonContext.OwnedGLCode = NormalizeGLCode(commonContext.OwnedGLCode);
            Console.WriteLine($"GL Code: {commonContext.OwnedGLCode}");
        }
        else
        {
            await _NewFolder.ClickAsync();
            await _FolderName.FillAsync(commonContext.OwnedFolderName);
            await _SelectGLCode.ClickAsync();
            await _SaveFolder.ClickAsync();
            commonContext.OwnedGLCode = await _ownGLCode.TextContentAsync();
            commonContext.OwnedGLCode= ExtractGLCode(commonContext.OwnedGLCode);
            commonContext.OwnedGLCode = NormalizeGLCode(commonContext.OwnedGLCode);
        }
    }

    public async Task AddNewSharedFolder(CommonContext commonContext)
    {
        await _ManageFolders.ClickAsync();
        bool isFolderPresent = await _SharedFolderName.IsVisibleAsync();
        if (isFolderPresent)
        {
            await _SharedFolderName.ClickAsync();
            commonContext.OwnedGLCode = await _ownGLCode.TextContentAsync();
            commonContext.OwnedGLCode= ExtractGLCode(commonContext.OwnedGLCode);
            commonContext.OwnedGLCode = NormalizeGLCode(commonContext.OwnedGLCode);
            Console.WriteLine($"GL Code: {commonContext.OwnedGLCode}");
        }
        else
        {
            await _NewFolder.ClickAsync();
            await _FolderName.FillAsync(commonContext.SharedFolderName);
            await _SelectGLCode.ClickAsync();
            await _SaveFolder.ClickAsync();
            await _shareFolder.ClickAsync();
            Task.Delay(3000).Wait();
            var companies = await Page.Locator("#dialog-operation-copyshare #treegridCompanies tbody tr td:nth-child(2) span").AllAsync();
            foreach (var company in companies)
            {
                if (await company.TextContentAsync() == "Avendra Demo Independent")
                {
                    await company.ClickAsync();
                    break;
                }
            }
            Task.Delay(5000).Wait();
            var users = await Page.Locator("#dialog-operation-copyshare #gview_treegridUsers tbody tr td:nth-child(2) span").AllAsync();
            foreach (var user in users)
            {
                if (await user.TextContentAsync() == "Jason Storch - Buyer")
                {
                    await user.ClickAsync();
                    break;
                }
            }
            await _ApplyButton.ClickAsync();
            //await _successToaster.WaitForAsync(new LocatorWaitForOptions { State = WaitForSelectorState.Visible });
            Task.Delay(9000).Wait();
            await _closeButton.ClickAsync();
            commonContext.OwnedGLCode = await _ownGLCode.TextContentAsync();
            commonContext.OwnedGLCode= ExtractGLCode(commonContext.OwnedGLCode);
            commonContext.OwnedGLCode = NormalizeGLCode(commonContext.OwnedGLCode);

        }
    }

    public async Task AddProductsToOwnedFolder(CommonContext commonContext)
    {
        await _selectLineItem.ClickAsync();
        await _addProducts.ClickAsync();
        await _FolderSelect.SelectOptionAsync(new SelectOptionValue { Label = "Owned Folder" });
        await _AddFavoritesBtn.ClickAsync();
        await _favoritesProductsLink.ClickAsync();
        await _OpenOwnedFolder.ClickAsync();
        await _summaryViewButton.ClickAsync();
        await _filterItem.TypeAsync(commonContext.ItemNumbers[0], new() { Delay = 100 });
        string supplierName = await _supplierName.TextContentAsync();
        commonContext.SupplierName = supplierName;

        // Set the flag to indicate products were added to the owned folder
        commonContext.AddedProductsToOwnedFolder = true;
        Console.WriteLine("Products added to Owned Folder - Flag set to true");
    }
    public async Task AddProductsToSharedFolder(CommonContext commonContext)
    {
        await _selectLineItem.ClickAsync();
        await _addProducts.ClickAsync();
        await _FolderSelect.SelectOptionAsync(new SelectOptionValue { Label = "Shared Folder" });
        await _AddFavoritesBtn.ClickAsync();
        await _favoritesProductsLink.ClickAsync();
        await _OpenSharedFolder.ClickAsync();
        await _summaryViewButton.ClickAsync();
        await _filterItem.TypeAsync(commonContext.ItemNumbers[0], new() { Delay = 100 });
        string supplierName = await _supplierName.TextContentAsync();
        commonContext.SupplierName=supplierName;
        commonContext.AddProductsToSharedFolder = true;
    }

    /// <summary>
    /// Verifies the GL code from favorites and optionally verifies quantity
    /// </summary>
    /// <param name="commonContext">The CommonContext containing data</param>
    /// <param name="verifyQuantity">Flag indicating whether to verify quantity (default: false)</param>
    /// <returns>Task representing the asynchronous operation</returns>
    public async Task VerifyGLCodeFromFavorites(CommonContext commonContext, bool verifyQuantity = false, bool verifyUOM = false)
    {
        try
        {
            // Log the supplier name we're looking for
            Console.WriteLine($"Looking for supplier: {commonContext.SupplierName}");

            // Click on the dropdown to open it
            await Supplier.ClickAsync();
            await Task.Delay(1000);
            Console.WriteLine("Dropdown list is visible");
            var countryRoseSelector = Page.FrameLocator("#v4Container").Locator($"span.k-list-item:has-text('{commonContext.SupplierName}')");
            await countryRoseSelector.ClickAsync();

            // Wait for the Create button to be visible
            await CreateButton.WaitForAsync(new LocatorWaitForOptions
            {
                State = WaitForSelectorState.Visible,
                Timeout = 5000
            });

            var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
            {
                await CreateButton.ClickAsync();
                await _confirmCreate.EvaluateAsync("node=>node.click()");
            });

            // Wait for the new page to load
            BringSecondPageToFront(newPage,commonContext);
            await _invPopupBtn.ClickAsync();

            // Verify quantity if requested
            if (verifyQuantity)
            {
                Console.WriteLine("Verifying quantity as requested by 'Then Verify Quantity' step...");
                await VerifyQuantity(commonContext);
            }

            //Verify UOM if requested
            if (verifyUOM)
            {
                Console.WriteLine("Verifying UOM as requested by 'Then Verify UOM' step...");
                await VerifyUOM(commonContext);
            }

            String ExpectedGLCode=await _glCodeDropdown.TextContentAsync();
            string TrimmedGLCode = ExtractGLCode(ExpectedGLCode);
            // Normalize the GL code by removing hyphens, periods, and extra spaces
            TrimmedGLCode = NormalizeGLCode(TrimmedGLCode);
            Console.WriteLine($"Expected GL Code: {TrimmedGLCode}");
            //Assert.AreEqual(TrimmedGLCode, commonContext.OwnedGLCode, "GL Codes do not match!");
            //Assert.AreEqual(TrimmedGLCode, commonContext.SpecificGL, "GL Codes do not match!");
            // Debug information to understand which branch is being executed
            Console.WriteLine($"Debug - SpecificGL: '{commonContext.SpecificGL ?? "null"}'");
            Console.WriteLine($"Debug - OwnedGLCode: '{commonContext.OwnedGLCode ?? "null"}'");
            Console.WriteLine($"Debug - TrimmedGLCode: '{TrimmedGLCode}'");
            Console.WriteLine($"Debug - AddedProductsToOwnedFolder flag: {commonContext.AddedProductsToOwnedFolder}");

            // Check if products were added to the owned folder
            if (commonContext.AddedProductsToOwnedFolder)
            {
                Console.WriteLine("Using OwnedGLCode for comparison because products were added to the Owned Folder");
                Assert.That(TrimmedGLCode, Is.EqualTo(commonContext.OwnedGLCode), "GL Codes do not match!");
            }
            else if(commonContext.AddProductsToSharedFolder)
            {
                Console.WriteLine("Using OwnedGLCode for comparison because products were added to the Shared Folder");
                Assert.That(TrimmedGLCode, Is.EqualTo(commonContext.OwnedGLCode), "GL Codes do not match!");
            }
            // Otherwise, use SpecificGL if available
            else if (!string.IsNullOrWhiteSpace(commonContext.SpecificGL))
            {
                Console.WriteLine("Using SpecificGL for comparison");
                Assert.That(TrimmedGLCode, Is.EqualTo(commonContext.SpecificGL), "GL Codes do not match!");
            }
            // Fallback to OwnedGLCode if SpecificGL is not available
            else
            {
                Console.WriteLine("Falling back to OwnedGLCode for comparison");
                Assert.That(TrimmedGLCode, Is.EqualTo(commonContext.OwnedGLCode), "GL Codes do not match!");
            }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in VerifyGLCodeFromFavorites: {ex.Message}");
                throw;
            }
    }
    private static string ExtractGLCode(string glCode)
    {
        // Split the GL code by '-' and take only the first two parts (e.g., "0001-03")
        var parts = glCode.Split('-');
        if (parts.Length >= 2)
        {
            // Combine the first two parts and trim any extra spaces
            string extractedCode = $"{parts[0]}-{parts[1]}".Trim();

            // If there is additional text after the GL code, remove it
            return extractedCode.Split(' ')[0]; // Take only the first part before any spaces
        }
        return glCode.Trim(); // If splitting fails, return the original trimmed GL code
    }
    private static string NormalizeGLCode(string glCode)
    {
        return glCode
        .Replace(".", "") // Remove periods
        .Replace(" ", "") // Remove extra spaces
        .Trim();          // Trim leading and trailing whitespace
    }

    /// <summary>
    /// Verifies that the CommonContext total quantity matches the value from the _currentDocQty locator.
    /// </summary>
    /// <param name="commonContext">The CommonContext containing the QuantityTotal</param>
    /// <returns>Task representing the asynchronous operation</returns>
    public async Task VerifyQuantity(CommonContext commonContext)
    {
        // Log the start of verification
        Console.WriteLine("Starting verification of quantity against UI value...");

        // Get the expected total from CommonContext
        float? expectedTotal = commonContext.QuantityTotal;

        // Check if QuantityTotal is null
        if (!expectedTotal.HasValue)
        {
            throw new InvalidOperationException("QuantityTotal is not set in CommonContext. Cannot verify against UI.");
        }

        // Get the current document quantity from the UI
        string currentDocQtyText = await _currentDocQty.InputValueAsync();

        // Parse the value to a float
        if (!float.TryParse(currentDocQtyText, out float currentDocQty))
        {
            throw new InvalidOperationException($"Could not parse current document quantity '{currentDocQtyText}' to a number.");
        }

        Console.WriteLine($"Current document quantity from UI: {currentDocQty}");
        Console.WriteLine($"Expected total quantity from CommonContext: {expectedTotal}");

        // Verify that the CommonContext total matches the current document quantity from the UI
        // Using a small epsilon for float comparison to handle potential floating-point precision issues
        const float epsilon = 0.0001f;
        bool isMatchWithUI = Math.Abs(expectedTotal.Value - currentDocQty) < epsilon;

        // Assert that the CommonContext total matches the current document quantity
        Assert.That(isMatchWithUI, Is.True,
            $"The expected total in CommonContext ({expectedTotal}) does not match the current document quantity in the UI ({currentDocQty}).");

        Console.WriteLine("UI quantity verification completed successfully.");
    }
    public async Task VerifyUOM(CommonContext commonContext)
    {
        // Log the start of verification
        Console.WriteLine("Starting verification of UOM against UI value...");
        string UOMText = await _uomField.InputValueAsync();
        Assert.That(UOMText, Is.EqualTo("EA"));

    }
    public async Task VerifyLineItem(CommonContext commonContext)
    {
        var expectedCurrentDocQty=await _currentDocQty.InputValueAsync();
        var expectedItemNumber=await _itemNumberField.InputValueAsync();
        var expectedDescription=await _descriptionField.InputValueAsync();
        var expectedGLCode=await _glCodeDropdown.TextContentAsync();
        var expectedUOM=await _uomField.InputValueAsync();
        await _changePObtn.ClickAsync();
        await _searchPO.FillAsync(commonContext.PoNumbers[1]);
        await _nextBtn.ClickAsync();
        var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await _applyToThisPO.ClickAsync();
        });
        BringSecondPageToFront(newPage,commonContext);
        await _invPopupBtn.ClickAsync();
        bool validationPassed = false;
        var frameLocator = Page.FrameLocator("#v4Container");
        var rows = await frameLocator.Locator("#invoiceLineItemGrid table tbody tr").AllAsync();
        var rowCount = rows.Count;
        for (int i = 0; i < rowCount; i++)
        {
            var row = frameLocator.Locator("#invoiceLineItemGrid table tbody tr").Nth(i);

            // Extract column values for the current row
            var currentDocQty = await row.Locator("td").Nth(4).InnerTextAsync();
            var itemNumber = await row.Locator("td").Nth(0).InnerTextAsync();
            var description = await row.Locator("td").Nth(1).InnerTextAsync();
            var GLCode = await row.Locator("td").Nth(2).InnerTextAsync();
            var uom = await row.Locator("td").Nth(3).InnerTextAsync();

            // Validate values
            if (currentDocQty == expectedCurrentDocQty &&
                itemNumber == expectedItemNumber &&
                description == expectedDescription &&
                GLCode == expectedGLCode &&
                uom == expectedUOM)
            {
                Console.WriteLine($"Validation passed for row {i + 1}.");
                validationPassed = true;
                break;
            }
        }
        //Assert.AreEqual(currentDocQty, await _currentDocQty.InputValueAsync());
        //Assert.AreEqual(itemNumber, await _itemNumberField.InputValueAsync());
        //Assert.AreEqual(description, await _descriptionField.InputValueAsync());
        //Assert.AreEqual(GLCode, await _glCodeDropdown.TextContentAsync());
        //Assert.AreEqual(uom, await _uomField.InputValueAsync());
    }
}
