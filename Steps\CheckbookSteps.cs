using TechTalk.SpecFlow;
using SpecFlowProject.Pom.Pages;
using SpecFlowProject.Hooks;
using System.Collections;
using SpecFlowProject.BusinessObjects;
using TechTalk.SpecFlow.Infrastructure;

namespace SpecFlowProject.Steps
{
    [Binding]
    public class CheckbookSteps
    {
        readonly Context _context;
        readonly CheckbookPage _checkbookPage;

        private ScenarioContext _scenarioContext;
        private readonly ISpecFlowOutputHelper _specFlowOutputHelper;

        public CheckbookSteps(Context context,ScenarioContext scenarioContext, ISpecFlowOutputHelper specFlowOutputHelper)
        {
            _context = context;
            _checkbookPage = new CheckbookPage(_context.Page!);
            _scenarioContext = scenarioContext;
            _specFlowOutputHelper = specFlowOutputHelper;
        }
        [Then(@"Open checkbook")]
        public async Task OpenCheckbook()
        {
            await _checkbookPage.ExpandAndOpenCheckBook((CommonContext)_scenarioContext["commonContext"]);
        }

        [Then(@"Open checkbook no checks")]
        public async Task OpenCheckbookNoChecks()
        {
            await _checkbookPage.OpenCheckbookNoChecks((CommonContext)_scenarioContext["commonContext"]);
        }


        [Then(@"Open checkbook and filter for GL code where status is ""([^""]*)""")]
        public async Task ThenOpenCheckbookAndFilterForGLCodeWhereStatusIsAndSetForcastAmountToLesthanZero(string exempt)
        {
            await _checkbookPage.OpenCheckbookandreturnExmptGLcodes((CommonContext)_scenarioContext["commonContext"], GLstatus: exempt);
            //await _checkbookPage.SetupForecastAmountToLessThanZero();
            await _checkbookPage.CaptureGLCode((CommonContext)_scenarioContext["commonContext"], _specFlowOutputHelper);
        }


        [Then(@"Open checkbook with return")]
        public async Task OpenCheckbookWithRerun()
        {   
            await _checkbookPage.ExpandAndOpenCheckBookReturn((CommonContext)_scenarioContext["commonContext"]);
        }
        [Then(@"Open checkbook With GlMasks")]
        public async Task OpenCheckbookWithMasks()
        {
            await _checkbookPage.ExpandAndOpenCheckBookWithGlMasks((CommonContext)_scenarioContext["commonContext"]);
        }
        

        [Then(@"Select Department and ShowAll Filter And Verify Data")]
        public async Task VerifyCheckbookDropdown()
        {
            await _checkbookPage.ClickDepartments();
        }

        [Then(@"Click Year View")]
        public async Task ClickYearView()
        {
            //Type the search term and press enter
            await _checkbookPage.ClickYearView();
        }

        private async Task VerifyGrid(Table table){
            foreach(TableRow row in table.Rows){
               string column = row["columns"];
               await _checkbookPage.VerifyDataGrid(column);
            }
        }

        [Then(@"Verify Grid fields for PeriodView")]
        public async Task VerifyPeriodViewGridHeader(Table table)
        {
            await VerifyGrid(table);
        }

        [Then(@"Verify Grid fields for YearView")]
        public async Task VerifyYearViewGridHeader(Table table)
        {
            await VerifyGrid(table);
        }


        [Then(@"Add GLCodes and save")]
        public async Task AddGLCodes(Table table)
        {
            List<GlCodes> glList = new List<GlCodes>();
            foreach(TableRow row in table.Rows){
                glList.Add(new GlCodes(row["forcast amount"],row["Fixed"],row["active"]));
            }
            ((CommonContext)_scenarioContext["commonContext"]).GLCodesList = glList;
            await _checkbookPage.AddNewGlCodes((CommonContext)_scenarioContext["commonContext"]);
        }

        [Then(@"Update/Verify Adjust Forecast Amount by %")]
        public async Task AdjustForecastAmt()
        {
            await _checkbookPage.AdjustForecastAmts((CommonContext)_scenarioContext["commonContext"]);
        }

        [Then(@"Add/Verify journal entry")]
        public async Task AddJournalEntry()
        {
            await _checkbookPage.AddJournalEntry((CommonContext)_scenarioContext["commonContext"]);
        }
        
        [Then(@"Verify checkbook and return")]  
        public async Task VerifyCheckBook(){
            await _checkbookPage.VerifyCheckBook(((CommonContext)_scenarioContext["commonContext"]),true);
        }

        [Then(@"Verify checkbook")]
        public async Task VerifyCheckBook1(){
            await _checkbookPage.VerifyCheckBook(((CommonContext)_scenarioContext["commonContext"]));
        }

        [Then(@"Edit and Verify GLCode Description, Forecast Amount, Active and Fixed Codes(.*)")]
        public async Task EditGlCodes(string yearview)
        {
            await _checkbookPage.EditGlCodes((CommonContext)_scenarioContext["commonContext"],!string.IsNullOrEmpty(yearview));
        }

        [Then(@"Delete/verify GLCodes(.*)")]
        public async Task DeleteGLCodes(string yearview)
        {
            await _checkbookPage.DeleteGLCodes((CommonContext)_scenarioContext["commonContext"],!string.IsNullOrEmpty(yearview));
        }

        [Then(@"Undelete/Verify GLCodes(.*)")]
        public async Task UnDeleteGLCodes(string yearview)
        {
            await _checkbookPage.UnDeleteGLCodes((CommonContext)_scenarioContext["commonContext"],!string.IsNullOrEmpty(yearview));
        }

        [Then(@"Add GLCodes and save with period copyacross")]
        public async Task AddGlCodesWithPeriod(Table table)
        {
            List<GlCodes> glList = new List<GlCodes>();
            foreach(TableRow row in table.Rows){
                glList.Add(new GlCodes(row["forcast amount"],row["budget amount"],row["Fixed"],row["active"]));
            }
            ((CommonContext)_scenarioContext["commonContext"]).GLCodesList = glList;
            await _checkbookPage.AddGlCodesWithPeriod((CommonContext)_scenarioContext["commonContext"]);
        }

        [Then(@"Verify Invoice amounts is matching with checkbook amount")]
        public async Task VerifyPOWithAmounts()
        {
            await _checkbookPage.ExpandAndOpenCheckBook((CommonContext)_scenarioContext["commonContext"]);
            await _checkbookPage.VerifyPOWithAmounts((CommonContext)_scenarioContext["commonContext"],"actualized");
        }

        [Then(@"Open Checkbook")]
        public async Task OpenCheckbookNoExpansion()
        {
            await _checkbookPage.OpenCheckbook((CommonContext)_scenarioContext["commonContext"]);
        }
        [Then(@"Verify Grid fields in Checkbook")]
        public async Task VerifyDataGrid(Table table)
        {
            foreach(TableRow row in table.Rows)
            {
               string column = row["columns"];
               await _checkbookPage.VerifyDataGrid(column);
            }
        }

        [Then(@"Select All checkboxes in the ColumnDropdown")]
        public async Task SelectAllCheckboxesInColumnDropdown()
        {
            await _checkbookPage.SelectAllCheckboxesInColumnDropdown();
        }

        [Then("Validate Hold amount on PO.")]
        public async Task ValidateHoldPo()
        {
            await _checkbookPage.ExpandAndOpenCheckBook((CommonContext)_scenarioContext["commonContext"]);
            await _checkbookPage.VerifyPOWithAmounts((CommonContext)_scenarioContext["commonContext"],"actualized");
        }

        

        [Then(@"VerifyPlannedAmt")]
        public async Task VerifyPlannedAmount()
        {
            await _checkbookPage.VerifyPlannedAmt((CommonContext)_scenarioContext["commonContext"]);
        }

        [Then(@"Verify CommittedAmt")]
        public async Task VerifyCommittedAmount()
        {
            await _checkbookPage.VerifyCommittedAmt((CommonContext)_scenarioContext["commonContext"]);
        }

        [Then(@"Verify ActualizedAmt")]
        public async Task VerifyActualizedAmount()
        {
            await _checkbookPage.VerifyActualizedAmt((CommonContext)_scenarioContext["commonContext"]);
        }

        [Then(@"exempt all GL Codes")]
        public async Task ExemptAllGLCodes()
        {
            await _checkbookPage.ExemptAllGLCodes();
        }
        [Then(@"Verify Exempt Assign users in PO Workflows")]
        public async Task ExemptGLCodeAll()
        {
            await _checkbookPage.ExemptGLCodeinPOWorkflow();
        }
        [Then(@"Verify Exempt Assign users in Invoice Workflows")]
        public async Task ExemptGLCodeAll1()
        {
            await _checkbookPage.ExemptGLCodeinInvoiceWorkflow();
        }
    }
}