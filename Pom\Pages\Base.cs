using Microsoft.Playwright;
using SpecFlowProject.BusinessObjects;
using System.Globalization;
using TechTalk.SpecFlow;
using NUnit.Framework;
using System.Collections;

using System.Text.Json;
using ClosedXML.Excel;
using Newtonsoft.Json.Linq;
using TechTalk.SpecFlow.Infrastructure;
using System.Text.Json.Nodes;

using System.Reflection.Metadata.Ecma335;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;

using SpecFlowProject.Utils;

namespace SpecFlowProject.Pom.Pages
{
    public class Base
    {
        protected IPage Page;

        public Base(IPage page) {
            Page = page;        
        }
        /*public Base(IPage page,ISpecFlowOutputHelper specFlowOutputHelper) {
            Page = page;
            _specFlowOutputHelper = specFlowOutputHelper;
        }*/

        public IPage GetPage() => Page;

        protected ILocator _marketPlaceLink => Page.Locator("h2:has-text('Marketplace')");
        protected ILocator _invoiceManagerLink => Page.Locator("h2:has-text('Invoice Manager')");
        protected ILocator _checkbookLink => Page.Locator("h2:has-text('Checkbook')");
        protected ILocator _viewEditChecbook => Page.Locator("a[href='/Buyer/Checkbook']");
        protected ILocator _viewEditPeriods => Page.Locator("a[href='/Buyer/Checkbook/GlPeriods']");
        
        protected ILocator _viewGlMask => Page.Locator("a[href='/Buyer/Checkbook/GLCodeFormats']");
        protected ILocator _offLinePos => Page.Locator("h2:has-text(\"Offline PO's\")");

        protected ILocator _offLinePosActionRequired => Page.GetByRole(AriaRole.Link, new() { Name = "Action Required" });

        protected ILocator _approvalPOLink => Page.Locator("#gview_poList #poList tr:last-child td.standardHref a");
        protected ILocator _createPoLink => Page.Locator("a[href='/Buyer/OfflinePurchaseOrder/CreateOfflinePO']");
        protected ILocator _enterInvoiceLink => Page.Locator("a[href='/Buyer/Invoices/EnterInvoice']");
        protected ILocator _actionRequiredInvoiceLink => Page.Locator("a[href='/Buyer/Invoices/ActionRequiredInvoices']");
        protected ILocator _scanManagerInvoiceLink => Page.Locator("a[href='/Buyer/Invoices/ScanManagerInvoices']");
        protected ILocator _processInvoicesLink => Page.Locator("a[href='/Buyer/Invoices/ProcessInvoices']");
        protected ILocator _viewInvoicesLink => Page.Locator("a[href='/Buyer/Invoices/ViewInvoices']");
        protected ILocator _actionRequiredLink => Page.Locator("#invoiceManagerNavItems a[href='/Buyer/Invoices/ActionRequiredInvoices']");
        protected ILocator _viewDepartmentsLink => Page.Locator("a[href='/Buyer/Checkbook/Departments']");

        protected ILocator _editCheckbookConfigurationLink => Page.Locator("a[href='/Buyer/Checkbook/GLCodeFormats']");
        protected IFrameLocator _iframe => Page.FrameLocator("#v4Container");

        protected ILocator _companyText => _iframe.Locator(".font-bold:has-text('Please Select a Company')");

        protected ILocator _companyText1 => _iframe.GetByText("Please Select a Company");
        protected ILocator _company => _iframe.Locator(".__selectCompanyDropdown");
        protected ILocator _companySelect => _iframe.Locator(".k-list ul li:nth-child(1)");
        protected ILocator _nextBtn => _iframe.GetByRole(AriaRole.Button, new() { Name = "Next" });

        protected ILocator _backBtn =>  _iframe.Locator("button:has-text('Back')");

        protected ILocator continueWithoutattachemnt => _iframe.GetByRole(AriaRole.Button, new() { Name = "Continue without Attachment" });
        protected ILocator _closePopupBtn => _iframe.Locator(".k-dialog-buttongroup button:has-text('Close')");
        protected ILocator _uploadBtn => _iframe.Locator("button:has-text('Upload')");
        protected ILocator _showScans => _iframe.Locator("button:has-text('Scans')");
        protected ILocator _hideScans => _iframe.Locator("button:has-text('Scans')");
        protected ILocator _closePOBtn => _iframe.Locator("button:has-text('Close PO')");
        protected ILocator _uploadDoc => _iframe.Locator("input[title='No files selected']");
        protected ILocator _uploadClose => _iframe.Locator(".flex.justify-end.gap-2>button");
        protected ILocator _saveBtn => _iframe.Locator("button:has-text('Save')");

        protected ILocator _CancelAppBtn => _iframe.GetByRole(AriaRole.Button, new() { Name = "Cancel", Exact = true });
        protected static readonly string UPLOADPDFFILE = ".\\UploadFiles\\iBuyEfficient_Receipt.pdf";
        protected ILocator _completeBtn => _iframe.Locator("button:has-text('Complete')");
        protected ILocator _createNewDocument => _iframe.GetByRole(AriaRole.Button, new() { Name = "Create New Document" });
        protected ILocator _invoiceCreditBtn => _iframe.GetByRole(AriaRole.Button, new() { Name = "Invoice / Credit" });
        protected ILocator _linkToSelectedBtn => _iframe.Locator("button:has-text('Link to Selected')");
        protected ILocator removeProxyUser => Page.Locator("a[href='/Account/RemoveProxyUser']");
        protected ILocator _linkToSelectedBtnErr => _iframe.Locator("p:has-text('You must link a completed receipt')");
        protected ILocator _cancelSelectedDocumentBtn => _iframe.Locator("button:has-text('Cancel Selected Document(s)')");
        protected ILocator _createReceiptBtn => _iframe.GetByRole(AriaRole.Button, new() { Name = "Receipt" });
        protected ILocator _confirmBtn => _iframe.GetByRole(AriaRole.Button, new() { Name = "Confirm" });
        protected ILocator _confirmCloseBtn => _iframe.GetByRole(AriaRole.Button, new() { Name = "Confirm and Close" });
        protected ILocator _navHeader => _iframe.Locator("nav");
        protected ILocator _invoiceHistoryBtn => _iframe.Locator("button:has-text('History')");
        protected ILocator _actionFilter => _iframe.Locator("#receiptHistoryGrid > div.k-grid-header > div > table > thead > tr > th:nth-child(1) > span.k-cell-inner > div > span");
        protected ILocator _filterInput => _iframe.Locator(".k-filter-menu input");
        protected ILocator _filterBtn => _iframe.Locator(".k-filter-menu button:has-text('Filter')");
        private ILocator _filterRpw => _iframe.Locator("#receiptHistoryGrid tbody tr td:nth-child(1)");
        protected ILocator _closeBtn => _iframe.Locator(".k-widget.k-window.k-dialog .flex.justify-end button");
        protected ILocator _closeBtnX =>_iframe.Locator(".k-window-actions.k-dialog-actions");
        protected ILocator _receiving => Page.Locator("h2:has-text('Receiving')");
        protected ILocator _viewReceiptsBtn => Page.Locator("#receivingNavItems li:nth-child(3)");
        protected ILocator _reports => Page.Locator("h2:has-text('Reports')");
        protected ILocator _enterVarienceBtn => Page.Locator("a[href='/Buyer/Receiving/Variances']");
        protected ILocator _homeLink => Page.Locator("h2:has-text('Home')");
        private ILocator _errorMessageDiv => _iframe.Locator(".k-widget.no_Veritcal_ScrollBar .k-grid-container table tbody tr:nth-child(1) td:nth-child(2)");
        private ILocator _errorMessage => _iframe.Locator(".k-widget.no_Veritcal_ScrollBar .k-grid-container table tbody tr:nth-child(1) td");
        private ILocator _getCountSpan => _iframe.Locator(".k-pager-info.k-label");

        private ILocator _getCountSpanSide => _iframe.Locator(".slide-pane .k-pager-info.k-label");
        private ILocator _loadingMask => _iframe.Locator(".k-loading-mask");

        protected ILocator _mpaAccountingPeriodDyna(int i) => Page.FrameLocator("#v4Container").Locator(".k-grid-table > tbody > tr:nth-child("+i+") .k-dropdowntree.k-picker");

        protected ILocator _mpaAccountingPeriodText(int i,string x) => _mpaAccountingPeriodDyna(i).Locator(".k-input-value-text:has-text('"+x+"')");
        private ILocator _mpaAccountingPeriodLast => Page.FrameLocator("#v4Container").Locator(".k-grid-table > tbody > tr.k-master-row:last-child .k-dropdowntree.k-picker");

        protected ILocator _mpaAccountingPeriodLastText(string x) => _mpaAccountingPeriodLast.Locator(".k-input-value-text:has-text('"+x+"')");

        protected ILocator _mpaAccountingPeriodOpened => Page.FrameLocator("#v4Container").Locator(".k-popup > .k-treeview > .k-treeview-lines > li[aria-expanded='true'].k-treeview-item > .k-animation-container ul li .k-treeview-leaf-text span.cursor-default:not(:has(span))");

        protected ILocator _mpaAddNew => Page.FrameLocator("#v4Container").Locator("button:has-text('Add New Row')");

        protected ILocator _mpaAccountingPeriodSelect => Page.FrameLocator("#v4Container").Locator(".k-popup > .k-treeview > .k-treeview-lines > .k-treeview-item:nth-child(1) .k-treeview-toggle .k-icon");

        protected ILocator _distibuteBtn => Page.FrameLocator("#v4Container").Locator("button:has-text('Distribute Equally')"); 

        protected ILocator _saveModel => Page.FrameLocator("#v4Container").Locator("section > div > div > button.m-1.bg-primary:has-text('Save')");

        protected ILocator periodIds => Page.FrameLocator("#v4Container").Locator("#invoiceBudgetPeriodGrid > .k-grid-container .k-dropdowntree .k-input-value-text");

        private ILocator _bulkGLCodeUpdateBtn => Page.FrameLocator("#v4Container").Locator("button:has-text('Bulk GL Code Update')");

        private ILocator _selectDepartmentCB => Page.FrameLocator("#v4Container").Locator(".k-grid-table tr:nth-child(1) td:nth-child(1) input");

        private ILocator _assignedGLTableTH1 => Page.FrameLocator("#v4Container").Locator("#assignGLCodeTable table >thead > tr > th:nth-child(1) > input");

        private ILocator _assignSelectedGLCode1 => Page.FrameLocator("#v4Container").Locator("button:has-text('Assign Selected GL Codes')");

        private ILocator _clickFilterDept => Page.FrameLocator("#v4Container").Locator("#viewDepartmentTable .k-grid-header table thead tr th:nth-child(2) .k-grid-header-menu");
        private ILocator _clearFilters => Page.FrameLocator("#v4Container").Locator("button:has-text('Clear Filters')");
        protected async Task<bool> WaitUntilNotVisibleInTable(ILocator elem, float time=200,int times =10){
            if(times == 0){
                return false;
            }
            times--;
            await Page.WaitForTimeoutAsync(time);
            var text =await elem.InnerTextAsync()??"";
            if(text=="No records available"){
                return true;
            }else{  
                var ret = await IsVisibleAsync(elem, time, times);
                if(ret){
                    return true;
                }
            }
            return false;
        }

        protected async Task<bool> IsVisibleAsync(ILocator elem, float time=200,int times =10){
            if(times == 0){
                return false;
            }
            times--;
            await Page.WaitForTimeoutAsync(time);
            if(await elem.IsVisibleAsync()){
                return true;
            }else{  
                var ret = await IsVisibleAsync(elem, time, times);
                if(ret){
                    return true;
                }
            }
            return false;
        }
        //Bring the newly opened page into focus
        public void BringSecondPageToFront(IPage newPage,CommonContext commonContext){
            commonContext.BasePage.Insert(0,Page);//Moved into commom test as soon as 
            Page = newPage;
        }
        protected Dictionary<string,Dictionary<string,ItemDetails>> GetDataAsDict(CommonContext commonContext,bool isPO){
            Dictionary<string,Dictionary<string,ItemDetails>> poDict = new Dictionary<string,Dictionary<string,ItemDetails>>();
            var looper = isPO ? commonContext.PoItemDetails:commonContext.CompletedInvoiceItems;
            foreach (KeyValuePair<string, ItemDetails> kvp in looper)
            {
                var keys  = kvp.Key.Split("#");
                Dictionary<string, ItemDetails>? nested;
                if (poDict.TryGetValue(keys[1], out nested)){
                    nested.Add(keys[0], kvp.Value);      
                }else{
                    nested = new Dictionary<string, ItemDetails>();
                    nested.Add(keys[0], kvp.Value);
                    poDict.Add(keys[1], nested);
                }
            }
            return poDict;
        }
        //Bring base page to focus
        public async Task BringBasePageToFront(CommonContext commonContext)
        {
            await Page.CloseAsync();
            #pragma warning disable CS8601 // Possible null reference assignment.
            Page = commonContext.BasePage.FirstOrDefault();
;
            #pragma warning restore CS8601 // Possible null reference assignment.
            #pragma warning disable CS8602 // Dereference of a possibly null reference.
            await commonContext.BasePage.FirstOrDefault().BringToFrontAsync();

            commonContext.BasePage.RemoveAt(0);

            #pragma warning restore CS8602 // Dereference of a possibly null reference.
        }

        protected string getLatestPeriod(CommonContext commonContext){
            var periods1 = commonContext.Periods;
            List<DateTime> periods = new List<DateTime>(); 
            var cultureInfo = new CultureInfo("en-US");
            Dictionary<string,string> data = new Dictionary<string, string>();
            foreach(var item in periods1){
                string output = item.Split('(', ')')[1];
                var output1 = output.Split(" - ");
                data.Add(output1[0],output1[1]);
                var op2=output1[0]+" 00:00:00";
                periods.Add(DateTime.Parse(op2,cultureInfo));
            }

            
            // Filter out future periods
            var pastPeriods = periods.Where(p => p.CompareTo(DateTime.Today)<=0);
            // Find the closest or current period in the past
            var closestOrCurrentPeriod =
                pastPeriods.MinBy(p =>
                    Math.Abs((p - DateTime.Today)
                        .TotalDays)); // Get the first (closest or current) period in the past
            // If no past periods exist, find the closest period in the future
            if(pastPeriods.ToList<DateTime>().Count>0){
                var clp = closestOrCurrentPeriod.ToString("MM/dd/yy");
                return clp+ " - "+data[clp];
            }else{
                var cp = periods
                    .OrderBy(p =>
                    Math.Abs((p - DateTime.Today).TotalDays))
                .First().ToString("MM/dd/yy");
                return cp+ " - "+data[cp];
            }
        } 

        protected async Task ValidatePeriods(CommonContext commonContext){
            if(commonContext.HasMulti){
                foreach(var item in await periodIds.AllAsync()){
                    var text = await item.TextContentAsync()??"";
                    var per = commonContext.Periods.Find(p=>p.Contains(text));
                    Assert.IsTrue(per!=null,"");
                }
            }
        }

        protected async Task AddGLCodesToDepartment(string deptName){
            await _viewDepartmentsLink.ClickAsync();
            await this.SelectCompany();
            await _closeBtnX.ClickAsync();
            await WaitUntilLoaderHidden();
            await _selectDepartmentCB.ClickAsync();
            await _bulkGLCodeUpdateBtn.ClickAsync();
            await _assignedGLTableTH1.ClickAsync();
            await _assignSelectedGLCode1.ClickAsync();
            await _viewEditChecbook.ClickAsync();
            await this.SelectCompany();
            await _closeBtnX.ClickAsync();
        }
        protected async Task updateMPABase(string periods,CommonContext commonContext, bool addNew=false, bool updateCurrent=false,string validPeriod ="")
        {
            var p = Int32.Parse(periods);
            if(commonContext.Periods.Count == 0){
                commonContext.Periods.Add(await _mpaAccountingPeriodDyna(1).Locator(".k-input-value-text").TextContentAsync()??"");
            }
            var count = updateCurrent?commonContext.Periods.Count:addNew?p:p-1;
            var k = updateCurrent?commonContext.Periods.Count:addNew?p:p-1;
            List<string> oldPeriods = new List<string>();

            for(var i=0;i<k;i++){
                if(!updateCurrent){
                    await _mpaAddNew.ClickAsync();
                }

                if (addNew)
                {
                    await _mpaAccountingPeriodLast.ClickAsync();
                    var addedPer = await _mpaAccountingPeriodOpened.Nth(0).TextContentAsync() ?? "";
                    var match = commonContext.Periods.FirstOrDefault(stringToCheck => stringToCheck.Contains(addedPer));
                    if (match != null)
                    {
                        commonContext.Periods.Add(addedPer);
                    }
                    await _mpaAccountingPeriodOpened.Nth(0).ClickAsync();
                    await _mpaAccountingPeriodLastText(addedPer).WaitForAsync();
                }
                else if (updateCurrent)
                {
                    await _mpaAccountingPeriodDyna(i + 1).ClickAsync();
                    var x = await _mpaAccountingPeriodOpened.Nth(0).TextContentAsync() ?? "";
                    var match = commonContext.Periods.FirstOrDefault(stringToCheck => stringToCheck.Contains(x));
                    if (match != null)
                    {
                        await _mpaAccountingPeriodOpened.Nth(0).ClickAsync();
                        await _mpaAccountingPeriodText(i + 1, x).WaitForAsync();
                        commonContext.Periods.Add(x);
                    }
                    else
                    {
                        foreach (var input in await _mpaAccountingPeriodOpened.AllAsync())
                        {
                            var y = await input.TextContentAsync() ?? "";
                            var notExits1 = commonContext.Periods.FindIndex(s => s == y) == -1;
                            if (notExits1)
                            {
                                await input.ClickAsync();
                                await _mpaAccountingPeriodText(i + 1, y).WaitForAsync();
                                commonContext.Periods.Add(y);
                                break;
                            }
                        }
                    }
                }
                else if (validPeriod!=null)
                {
                    await _mpaAccountingPeriodDyna(i + 2).ClickAsync();
                    var resp = await _mpaAccountingPeriodOpened.Nth(0).TextContentAsync() ?? "";
                    commonContext.Periods.Add(resp);
                    await _mpaAccountingPeriodOpened.Nth(0).ClickAsync();
                    await _mpaAccountingPeriodText(i + 2, resp).WaitForAsync();
                }
                else{
                    await _mpaAccountingPeriodDyna(i + 2).ClickAsync();
                    var resp = await _mpaAccountingPeriodOpened.Nth(0).TextContentAsync() ?? "";
                    commonContext.Periods.Add(resp);
                    await _mpaAccountingPeriodOpened.Nth(0).ClickAsync();
                    await _mpaAccountingPeriodText(i + 2, resp).WaitForAsync();
                }
                count--;
            }
            await _distibuteBtn.ClickAsync();
            await _saveModel.ClickAsync();
        }
        //Selecting the company
        public async Task SelectCompany()
        {

            try{
                await _navHeader.First.WaitForAsync();
            }catch(Exception){}
            
            if(await IsVisibleAsync(_companyText,1000,5)){
                await _company.ClickAsync();
                await _companySelect.ClickAsync();
            }
            
        }
        private void ReadToken(string token,CommonContext commonContext){
            try{
                if(string.IsNullOrEmpty(token) && commonContext!=null && (commonContext.Roles==null || commonContext.Roles.Count==0)){
                    token = token.Replace("Bearer ", string.Empty);
                    commonContext.Roles = RandomGenerator.DecodeJwt(RandomGenerator.ConvertJwtStringToJwtSecurityToken(token)).ToList();
                }
            }catch(Exception e){
                Console.WriteLine(e.Message);
            }
        }


        public async Task<JObject> WaitForResponseAsyncObj(string url,CommonContext commonContext){
            try{
                var resp = await Page.WaitForResponseAsync(response => response.Url.IndexOf(url)!=-1);
                var token = await resp.Request.HeaderValueAsync("authorization");
                if (token != null)
                {
                    ReadToken(token, commonContext);
                }
                return JObject.Parse(System.Text.Encoding.UTF8.GetString(await resp.BodyAsync()));
            }catch(Exception){
                return new JObject();
            }
        }

        protected PageGotoOptions GetPgOptions(){
            PageGotoOptions pg =  new PageGotoOptions();
            pg.Timeout = 30000;
            return pg;
        }
        public async Task<JArray> WaitForResponseAsync(string url,CommonContext commonContext){
            try{
                var resp = await Page.WaitForResponseAsync(response => response.Url.IndexOf(url)!=-1);
                var token = await resp.Request.HeaderValueAsync("authorization");
                if (token != null)
                {
                    ReadToken(token, commonContext);
                }
                return JArray.Parse(System.Text.Encoding.UTF8.GetString(await resp.BodyAsync()));
            }catch(Exception){
                return new JArray();
            }
        }
        public async Task<JObject> WaitUntilRequestCompleteResp(string url,ILocator elem,CommonContext commonContext){
            var resp = await Page.RunAndWaitForResponseAsync(async () =>
            {
                await elem.ClickAsync();
            }, response => response.Url.IndexOf(url)!=-1 && response.Status == 200,new PageRunAndWaitForResponseOptions { Timeout = 30000 });
            var token = await resp.Request.HeaderValueAsync("authorization");
            if (token != null)
            {
                ReadToken(token, commonContext);
            }
            return JObject.Parse(System.Text.Encoding.UTF8.GetString(await resp.BodyAsync()));
        }
        //Capture data from Hold  and Spend Amount 
        public float GetAmount(string amount)
        {
            var hasNeg = false;
            if( !string.IsNullOrEmpty(amount) && amount.IndexOf("(")!=-1){
                amount = amount.Replace("(", string.Empty);
                amount = amount.Replace(")", string.Empty);
                hasNeg = true;
            }  
            //Console.WriteLine(amount+"sddd");
            var amt = float.Parse(amount, NumberStyles.AllowCurrencySymbol | NumberStyles.Currency);
            if(hasNeg){
                amt = -amt;
            }
            //Console.WriteLine(amt+"sddd");
            return (float)Math.Round(amt,2);
        }

        //Verify different Invoice Status 
        public async Task VerifyStatus(string status)
        {
            if(await IsVisibleAsync(_invoiceHistoryBtn,1000,5)){
                await _invoiceHistoryBtn.ClickAsync();
                await _actionFilter.ClickAsync();
                await _filterInput.WaitForAsync();
                await _filterInput.FillAsync(status);
                if(await _filterBtn.IsEnabledAsync()){
                    await _filterBtn.ClickAsync();
                    //string completedhistory=await _filterRpw.InnerTextAsync();
                    
                }
                await _closeBtn.ClickAsync();
            }
           await Task.Run(()=>{});
        }
        
        public async Task WaitUntilLoaderHidden(){
            if(await _loadingMask.CountAsync()>0){
                Thread.Sleep(100);
                await WaitUntilLoaderHidden();
            }
            await Task.Run(()=>{});
        }
        public async Task WaitUntilRequestCompleteUrl(string url){
            await Page.RouteAsync("**", async r =>
            {
                if(r.Request.Url.IndexOf(url)!=-1){
                    var response = await r.FetchAsync();
                    await r.FulfillAsync(new() { Response = response });
                }else{
                    await r.ContinueAsync();
                }
            });
        }
        //Wait method until API call is complete
        public async Task WaitUntilRequestComplete(ILocator elem1, string url){
            await Page.RunAndWaitForResponseAsync(async () =>
            {
                await elem1.ClickAsync();
            }, response => {
                if(response.Url.IndexOf(url)!=-1 && response.Status == 200){
                    
                }
                return true;
            });
        }

        
        
        // Verifying titles of the table header
        public async Task VerifyTitle(string name,bool forceElse = false)
        {
            if(!forceElse && name.IndexOf("Period")!=-1){
                await Assertions.Expect(Page.FrameLocator("#v4Container").Locator("table thead tr th .text-center:has-text('"+name+"')").Nth(0)).ToBeVisibleAsync();       
            }else{
                await Assertions.Expect(Page.FrameLocator("#v4Container").Locator("span[class='k-column-title']").GetByText(name, new() { Exact = true }).Nth(0)).ToBeVisibleAsync();
            }
        }

       //Waiting until element is enabled
        public async Task WaitUntilEnabled(ILocator name)
        {
            if(!await name.IsEnabledAsync()){
                Thread.Sleep(100);
                await WaitUntilEnabled(name);
            }
            await Task.Run(()=>{});
        }

        //Assertion to Verify in table 
        public async Task VerifyTitleInvoiceId(string name)
        {
            await Assertions.Expect(_iframe.Locator("#invoiceLineItemGrid span[class='k-column-title']").GetByText(name, new() { Exact = true })).ToBeVisibleAsync();
        }

        //Assertion to Verify GL Summary Grid summary present in invoice tables
        public async Task VerifyTitleGLSummary(string name)
        {
            await Assertions.Expect(_iframe.Locator("div:nth-child(4) > div:nth-child(2) > div:nth-child(1) > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > table:nth-child(1) > thead:nth-child(2) > tr:nth-child(1) > th > span:nth-child(1) > span:nth-child(1) > span[class='k-column-title']").GetByText(name, new() { Exact = true })).ToBeVisibleAsync();
        }

        //Assertion to Verify Periods Table Grid
        public async Task VerifyTitlePeriods(string name)
        {
            await Assertions.Expect(_iframe.Locator("span[class='k-link']").GetByText(name, new() { Exact = true }).Nth(0)).ToBeVisibleAsync();
        }

        //To Verify file upload error messages 
        public async Task<List<string>> ExtractRowDataAsync()
        {
            // Locate and switch to the iframe.     
            var rowData = new List<string>();
            await _errorMessageDiv.WaitForAsync();            
            foreach(var cell in await _errorMessage.AllAsync())
            {
                rowData.Add(await cell.TextContentAsync()??"");
            }
            return rowData;
           // return new List<string>();
        }

        //get total count of rows in table
        public async Task<int> getTotalgridCount(string end, bool sidePane = false){
            string text = string.Empty;
            if(sidePane){
                text = await _getCountSpanSide.TextContentAsync() ?? String.Empty;
            }else{
                text = await _getCountSpan.TextContentAsync() ?? String.Empty;
            }
            int pFrom = text.IndexOf("of ") + "of ".Length;
            int pTo = text.LastIndexOf(" "+end);
            String result = text.Substring(pFrom, pTo - pFrom);
            return int. Parse(result);
        }


       //Export data to excel
       public void VerifyExportToExcel(ArrayList excelTableTitles, string name)
       {
        var workbook = new XLWorkbook(name);
        var worksheet = workbook.Worksheet(1); // Assuming you want the first worksheet
        File.Delete(name);
       }

       public void ValidateExcel(string filePath, string[] expectedColumns,bool deleteFile = false){
            //Console.WriteLine($"File Path: '{filePath}'");
            using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                var workbook = new HSSFWorkbook(fileStream); // For .xls files
                var sheet = workbook.GetSheet("Sheet1"); // Get the sheet named "Sheet1"

                if (sheet == null)
                {
                    Assert.Fail("Sheet1 not found in the Excel file.");
                }

                var row = sheet?.GetRow(0); // Read the first row (A1:V1)

                if (row == null)
                {
                    Assert.Fail("No row found in Sheet1.");
                }

                var columns = new string[row?.LastCellNum ?? 0];
                for (int i = 0; i < row?.LastCellNum; i++)
                {
                    var cell = row.GetCell(i);
                    columns[i] = cell?.ToString() ?? "NULL"; // Extract column headers
                }
                bool columnsMatch = true;

                int minLength = Math.Min(columns.Length, expectedColumns.Length);

                for (int i = 0; i < minLength; i++)
                {
                    string actual = columns[i];
                    string expected = expectedColumns[i];

                    if (actual != expected)
                    {
                        columnsMatch = false;
                    }
                }
                // Final success message if no mismatches or extra columns
                if (!columnsMatch)
                {
                    Assert.Fail("Test failed because the columns doen't match.");
                }
            }
            if(deleteFile){
                File.Delete(filePath);
            }
       }

       public void WriteToExcel(string _filePath, string sheetName, int rowNum, string[] data){
            HSSFWorkbook workbook;

            using (var stream = new FileStream(_filePath, FileMode.Open, FileAccess.Read))
            {
                workbook = new HSSFWorkbook(stream);
            }
            var sheet = workbook.GetSheet(sheetName);
            var row = sheet.GetRow(rowNum); 
            for(int i=0;i<data.Length;i++){
                row.GetCell(i).SetCellValue(data[i]);
            }
            using (var stream = new FileStream(_filePath, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(stream);
            } 
        }

    }
    
}

