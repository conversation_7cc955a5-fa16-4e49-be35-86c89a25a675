@Checkbook
Feature: 10_Validate Checkbook amounts with invoice amount which was created using Multiple GL Codes in PO  
@MultipleGLCheckbookValidations
Scenario Outline: 22_Validate Checkbook amounts with invoice amounts using Multiple GL codes
	Given Open the Web url 
	Then Login to site with username and password
	Then HomePage Loaded
    Then Change user in work area with user <PERSON><PERSON><PERSON> - Stackular - Manager
    #Then Open checkbook and filter for GL code where status is "Required"
    Then Open checkbook with return
    #Then Create Po for supplier with specific GL Code
    Then Create Po for supplier
    Then  Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | |
        | 1 | 2 | | |
    Then click on Edit Periods button
	Then update MPA with 2 periods.
    Then Submit and send for approval to Jason-Stackular Manager
    Then Change user in work area to parent
    Then Copy PO Url
    Then Verify checkbook and return    
    Then Submit and Approve PO.
    Then Verify committed amount in checkbook
    Then Expand Invoice Manager And Enter Invoice
    Then Select Company
    Then Enter Document Number with next
    Then Verify PO number in the Enter invoice PO grid
    Then Create invoice for PO created and save it
        | quantities|
    Then Complete Invoice
    Then Open First Div 
    Then Create another invoice with desired quantities and complete it
        |quantities|
        | 2.5 |
        | 3.7 |
    Then Create a credit memo with desired quantities and complete it 
        |quantities|
        | -3 |
        | -4 |    
    Then Close PO Action
    Then Verify Invoice amounts is matching with checkbook amount

@SingleGLCheckbookValidations
Scenario Outline: 23_Validate Checkbook amounts with invoice using single GL code
	Given Open the Web url 
	Then Login to site with username and password
	Then HomePage Loaded
    Then Open checkbook with return
    Then Create Po for supplier
    Then  Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | |
        | 1 | 2 | | |
    Then Submit and Approve PO.
    Then Expand Invoice Manager And Enter Invoice
    Then Select Company
    Then Enter Document Number with next
    Then Verify PO number in the Enter invoice PO grid
    Then Create invoice for PO created and save it
        | quantities|
        | 6 |
    Then update mpa adding new row     
    Then Complete Invoice
    Then Open First Div
    Then Create a credit memo with desired quantities
        |quantities|
        | -3|
    Then update mpa adding new row    
    Then Complete New Invoice    
    Then Close PO Action
    Then Verify Invoice amounts is matching with checkbook amount

@DLineItem
Scenario Outline: 24_Validate Checkbook amounts with invoice using single GL code
    Given Open the Web url 
	Then Login to site with username and password
	Then HomePage Loaded
    Then Change user in work area with user Nurul Alam - Stackular - Manager
    Then Open checkbook and filter for GL code where status is "Exempt"
    Then Create Po for supplier
    Then  Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | |
        | 1 | 2 | | |
    Then Submit and send for approval to Jason-Stackular Manager
    Then Verify checkbook
    Then Change user in work area to parent
    Then Copy PO Url   
    Then Submit and Approve PO.
    Then Verify committed amount in checkbook
